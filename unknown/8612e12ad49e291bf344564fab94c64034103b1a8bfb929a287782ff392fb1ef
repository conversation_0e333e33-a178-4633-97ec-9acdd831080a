"""
练习模板 - 中等级别

学习重点：
- 独立编写网络请求代码
- 理解错误处理
- 数据解析和展示
- 时间戳处理
"""

import requests
import json
from datetime import datetime

def get_iss_data():
    """
    任务1: 获取国际空间站数据
    
    要求：
    1. 定义API的URL: "http://api.open-notify.org/iss-now.json"
    2. 发送GET请求
    3. 检查状态码，如果是200则返回response，否则返回None
    4. 添加适当的打印信息告知用户请求状态
    """
    print("🚀 开始获取国际空间站位置...")
    
    # TODO: 在这里编写你的代码
    
    pass  # 删除这行，写入你的代码

def show_and_parse_data(response):
    """
    任务2: 显示原始数据并解析
    
    要求：
    1. 检查response是否为None
    2. 打印原始的response.text
    3. 将JSON字符串转换为Python字典
    4. 返回解析后的数据字典
    5. 如果解析失败，打印错误信息并返回None
    """
    print("\n📄 处理服务器数据...")
    
    # TODO: 在这里编写你的代码
    
    pass  # 删除这行，写入你的代码

def display_location_info(data):
    """
    任务3: 显示位置信息
    
    要求：
    1. 检查data是否为None
    2. 提取并显示以下信息：
       - 消息状态 (message)
       - 纬度 (iss_position -> latitude)
       - 经度 (iss_position -> longitude)
       - 时间戳 (timestamp)
    3. 将时间戳转换为可读格式并显示
    4. 添加合适的emoji和格式化输出
    """
    print("\n🌍 国际空间站位置报告:")
    
    # TODO: 在这里编写你的代码
    
    pass  # 删除这行，写入你的代码

def bonus_location_description(data):
    """
    挑战任务: 位置描述 (可选)
    
    要求：
    1. 根据纬度判断南北半球 (正数=北半球，负数=南半球)
    2. 根据经度判断东西半球 (正数=东半球，负数=西半球)
    3. 显示友好的位置描述
    
    例如：北纬45.2°, 东经120.5°
    """
    if data is None:
        return
    
    print("\n🗺️  位置描述:")
    
    # TODO: 在这里编写你的代码 (挑战任务，可选)
    
    pass  # 删除这行，写入你的代码

def main():
    """
    主程序
    """
    print("=" * 50)
    print("🛰️  国际空间站位置练习 - 中等级别")
    print("=" * 50)
    
    # 调用你编写的函数
    response = get_iss_data()
    data = show_and_parse_data(response)
    display_location_info(data)
    bonus_location_description(data)  # 挑战任务
    
    print("\n🎉 练习完成！")

if __name__ == "__main__":
    main()
