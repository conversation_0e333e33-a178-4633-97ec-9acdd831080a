"""
练习答案 - 简单级别
这是 practice_easy.py 的完整答案版本
"""

import requests
import json
from datetime import datetime

def get_iss_data():
    """
    步骤1答案: 获取国际空间站数据
    """
    print("🚀 开始获取国际空间站位置...")
    
    url = "http://api.open-notify.org/iss-now.json"
    
    # 答案: 使用requests.get()发送请求
    response = requests.get(url)
    
    # 答案: 获取并打印状态码
    print(f"状态码: {response.status_code}")
    
    # 答案: 判断请求是否成功
    if response.status_code == 200:
        print("✅ 请求成功!")
        return response
    else:
        print("❌ 请求失败!")
        return None

def show_raw_data(response):
    """
    步骤2答案: 显示服务器返回的原始数据
    """
    if response is None:
        return
    
    print("\n📄 服务器返回的原始数据:")
    # 答案: 打印response.text
    print(response.text)

def parse_data(response):
    """
    步骤3答案: 解析JSON数据
    """
    if response is None:
        return None
    
    # 答案: 将JSON字符串转换为Python字典
    data = json.loads(response.text)
    
    print("\n🌍 解析后的数据:")
    # 答案: 获取各个字段
    print(f"消息: {data['message']}")
    print(f"时间戳: {data['timestamp']}")
    print(f"纬度: {data['iss_position']['latitude']}")
    print(f"经度: {data['iss_position']['longitude']}")
    
    return data

def convert_timestamp(data):
    """
    步骤4答案: 转换时间戳
    """
    if data is None:
        return
    
    # 答案: 从数据中获取时间戳
    timestamp = data['timestamp']
    
    print(f"\n⏰ 时间戳转换:")
    print(f"时间戳数字: {timestamp}")
    
    # 答案: 将时间戳转换为可读时间
    readable_time = datetime.fromtimestamp(timestamp)
    print(f"转换为时间: {readable_time}")

def main():
    """
    主程序
    """
    print("=" * 50)
    print("🛰️  国际空间站位置练习 - 简单级别答案")
    print("=" * 50)
    
    response = get_iss_data()
    show_raw_data(response)
    data = parse_data(response)
    convert_timestamp(data)
    
    print("\n🎉 练习完成！")

if __name__ == "__main__":
    main()
