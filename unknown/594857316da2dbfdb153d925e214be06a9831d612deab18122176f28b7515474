"""
自动测试评分系统
用于评估 practice_hard.py 的完成质量

评分标准：
- 基础功能实现 (60分)
- 错误处理完善 (20分)  
- 代码结构清晰 (10分)
- 挑战功能实现 (10分)
"""

import importlib.util
import sys
import io
import contextlib
import traceback
import inspect
import requests
from datetime import datetime

class ISSGrader:
    def __init__(self, student_file="practice_hard.py"):
        self.student_file = student_file
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
        self.student_module = None
        
    def load_student_code(self):
        """加载学生的代码文件"""
        try:
            spec = importlib.util.spec_from_file_location("student_code", self.student_file)
            self.student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.student_module)
            return True
        except Exception as e:
            self.add_result("代码加载", 0, 5, f"无法加载代码文件: {e}")
            return False
    
    def add_result(self, test_name, score, max_score, comment=""):
        """添加测试结果"""
        self.test_results.append({
            'name': test_name,
            'score': score,
            'max_score': max_score,
            'comment': comment
        })
        self.total_score += score
    
    def test_class_structure(self):
        """测试类结构 (10分)"""
        print("🔍 测试1: 检查类结构...")
        
        if not hasattr(self.student_module, 'ISSTracker'):
            self.add_result("类定义", 0, 10, "未找到ISSTracker类")
            return
        
        cls = self.student_module.ISSTracker
        required_methods = [
            '__init__', 'get_iss_position', 'parse_data', 
            'display_info', 'run'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(cls, method):
                missing_methods.append(method)
        
        if missing_methods:
            score = max(0, 10 - len(missing_methods) * 2)
            self.add_result("类结构", score, 10, f"缺少方法: {', '.join(missing_methods)}")
        else:
            self.add_result("类结构", 10, 10, "类结构完整")
    
    def test_initialization(self):
        """测试初始化方法 (5分)"""
        print("🔍 测试2: 检查初始化...")
        
        try:
            tracker = self.student_module.ISSTracker()
            
            # 检查是否有URL属性
            has_url = any(hasattr(tracker, attr) and 'api.open-notify.org' in str(getattr(tracker, attr)) 
                         for attr in dir(tracker))
            
            if has_url:
                self.add_result("初始化", 5, 5, "正确设置API URL")
            else:
                self.add_result("初始化", 2, 5, "初始化成功但可能缺少URL设置")
                
        except Exception as e:
            self.add_result("初始化", 0, 5, f"初始化失败: {str(e)[:50]}")
    
    def test_get_position(self):
        """测试获取位置功能 (20分)"""
        print("🔍 测试3: 检查位置获取功能...")
        
        try:
            tracker = self.student_module.ISSTracker()
            
            # 捕获输出
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                result = tracker.get_iss_position()
            
            output = f.getvalue()
            
            if result is not None:
                # 检查是否是有效的响应对象
                if hasattr(result, 'status_code') and hasattr(result, 'text'):
                    self.add_result("位置获取", 20, 20, "成功获取ISS位置数据")
                else:
                    self.add_result("位置获取", 10, 20, "返回了数据但格式可能不正确")
            else:
                # 检查是否有网络请求相关的输出
                if any(keyword in output.lower() for keyword in ['请求', '连接', '状态', 'request']):
                    self.add_result("位置获取", 8, 20, "有网络请求逻辑但返回None")
                else:
                    self.add_result("位置获取", 0, 20, "未实现位置获取功能")
                    
        except Exception as e:
            error_msg = str(e)
            if 'requests' in error_msg.lower():
                self.add_result("位置获取", 5, 20, "有网络请求但存在错误")
            else:
                self.add_result("位置获取", 0, 20, f"位置获取功能错误: {error_msg[:50]}")
    
    def test_data_parsing(self):
        """测试数据解析功能 (15分)"""
        print("🔍 测试4: 检查数据解析...")
        
        try:
            tracker = self.student_module.ISSTracker()
            
            # 创建模拟响应对象
            class MockResponse:
                def __init__(self):
                    self.status_code = 200
                    self.text = '{"message": "success", "iss_position": {"latitude": "25.0", "longitude": "120.0"}, "timestamp": 1750000000}'
            
            mock_response = MockResponse()
            
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                result = tracker.parse_data(mock_response)
            
            if result is not None and isinstance(result, dict):
                # 检查是否包含必要的键
                required_keys = ['message', 'iss_position', 'timestamp']
                if all(key in result for key in required_keys):
                    self.add_result("数据解析", 15, 15, "正确解析JSON数据")
                else:
                    self.add_result("数据解析", 8, 15, "解析了数据但可能缺少某些字段")
            else:
                self.add_result("数据解析", 3, 15, "数据解析功能存在问题")
                
        except Exception as e:
            if 'json' in str(e).lower():
                self.add_result("数据解析", 5, 15, "有JSON解析逻辑但存在错误")
            else:
                self.add_result("数据解析", 0, 15, f"数据解析错误: {str(e)[:50]}")
    
    def test_display_info(self):
        """测试信息显示功能 (10分)"""
        print("🔍 测试5: 检查信息显示...")
        
        try:
            tracker = self.student_module.ISSTracker()
            
            # 模拟数据
            test_data = {
                'message': 'success',
                'iss_position': {'latitude': '25.0', 'longitude': '120.0'},
                'timestamp': 1750000000
            }
            
            f = io.StringIO()
            with contextlib.redirect_stdout(f):
                tracker.display_info(test_data)
            
            output = f.getvalue()
            
            # 检查输出是否包含关键信息
            keywords = ['纬度', '经度', '时间', 'latitude', 'longitude', '25.0', '120.0']
            found_keywords = sum(1 for keyword in keywords if keyword in output)
            
            if found_keywords >= 4:
                self.add_result("信息显示", 10, 10, "正确显示位置信息")
            elif found_keywords >= 2:
                self.add_result("信息显示", 6, 10, "显示了部分信息")
            else:
                self.add_result("信息显示", 2, 10, "信息显示功能不完整")
                
        except Exception as e:
            self.add_result("信息显示", 0, 10, f"信息显示错误: {str(e)[:50]}")
    
    def test_error_handling(self):
        """测试错误处理 (20分)"""
        print("🔍 测试6: 检查错误处理...")
        
        score = 0
        
        try:
            tracker = self.student_module.ISSTracker()
            
            # 测试None值处理
            try:
                f = io.StringIO()
                with contextlib.redirect_stdout(f):
                    tracker.parse_data(None)
                    tracker.display_info(None)
                score += 10
            except:
                pass
            
            # 检查代码中是否有try-except块
            source = inspect.getsource(self.student_module.ISSTracker)
            if 'try:' in source and 'except' in source:
                score += 10
            elif 'if' in source and ('None' in source or 'is None' in source):
                score += 5
            
            self.add_result("错误处理", score, 20, f"错误处理得分: {score}/20")
            
        except Exception as e:
            self.add_result("错误处理", 0, 20, f"无法测试错误处理: {str(e)[:50]}")
    
    def test_bonus_features(self):
        """测试挑战功能 (10分)"""
        print("🔍 测试7: 检查挑战功能...")
        
        bonus_score = 0
        bonus_features = []
        
        try:
            tracker = self.student_module.ISSTracker()
            
            # 检查是否有额外方法
            bonus_methods = ['save_data', 'track_movement', 'calculate_speed']
            for method in bonus_methods:
                if hasattr(tracker, method):
                    bonus_features.append(method)
                    bonus_score += 3
            
            # 检查是否有用户交互
            if hasattr(tracker, 'run'):
                source = inspect.getsource(tracker.run)
                if 'input(' in source:
                    bonus_features.append('用户交互')
                    bonus_score += 1
            
            bonus_score = min(bonus_score, 10)  # 最多10分
            
            if bonus_features:
                self.add_result("挑战功能", bonus_score, 10, f"实现了: {', '.join(bonus_features)}")
            else:
                self.add_result("挑战功能", 0, 10, "未实现挑战功能")
                
        except Exception as e:
            self.add_result("挑战功能", 0, 10, f"无法检查挑战功能: {str(e)[:50]}")
    
    def generate_report(self):
        """生成评分报告"""
        print("\n" + "="*60)
        print("🎯 自动评分报告")
        print("="*60)
        
        for result in self.test_results:
            status = "✅" if result['score'] == result['max_score'] else "⚠️" if result['score'] > 0 else "❌"
            print(f"{status} {result['name']}: {result['score']}/{result['max_score']} 分")
            if result['comment']:
                print(f"   💬 {result['comment']}")
        
        print("\n" + "-"*60)
        print(f"📊 总分: {self.total_score}/{self.max_score} 分")
        
        # 等级评定
        percentage = (self.total_score / self.max_score) * 100
        if percentage >= 90:
            grade = "A+ 优秀"
            emoji = "🏆"
        elif percentage >= 80:
            grade = "A 良好"
            emoji = "🥇"
        elif percentage >= 70:
            grade = "B+ 中等偏上"
            emoji = "🥈"
        elif percentage >= 60:
            grade = "B 中等"
            emoji = "🥉"
        else:
            grade = "C 需要改进"
            emoji = "💪"
        
        print(f"{emoji} 等级: {grade} ({percentage:.1f}%)")
        
        # 改进建议
        print("\n💡 改进建议:")
        if self.total_score < 60:
            print("- 先完成基础功能的实现")
            print("- 确保类结构和方法定义正确")
        elif self.total_score < 80:
            print("- 加强错误处理机制")
            print("- 完善数据显示格式")
        else:
            print("- 尝试实现更多挑战功能")
            print("- 优化用户交互体验")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始自动评分...")
        print(f"📁 测试文件: {self.student_file}")
        
        if not self.load_student_code():
            return
        
        # 运行所有测试
        self.test_class_structure()
        self.test_initialization()
        self.test_get_position()
        self.test_data_parsing()
        self.test_display_info()
        self.test_error_handling()
        self.test_bonus_features()
        
        # 生成报告
        self.generate_report()

def main():
    """主程序"""
    import os
    
    # 检查学生文件是否存在
    student_file = "practice_hard.py"
    if not os.path.exists(student_file):
        print(f"❌ 找不到文件: {student_file}")
        print("请确保你的代码文件名为 practice_hard.py")
        return
    
    # 运行评分
    grader = ISSGrader(student_file)
    grader.run_all_tests()
    
    print(f"\n📝 评分完成！你的代码得分: {grader.total_score}/100")

if __name__ == "__main__":
    main()
