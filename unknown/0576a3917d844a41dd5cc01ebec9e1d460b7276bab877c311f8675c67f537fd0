"""
练习模板 - 困难级别

项目要求：
创建一个完整的国际空间站追踪程序，实现以下功能：

核心功能：
1. 获取ISS实时位置数据
2. 解析并显示位置信息
3. 时间戳处理和显示
4. 错误处理和用户提示

扩展功能 (挑战)：
5. 多次获取数据，显示ISS移动轨迹
6. 计算ISS移动速度
7. 保存数据到文件
8. 创建简单的用户交互界面

技术要求：
- 使用面向对象编程 (创建类)
- 实现完整的错误处理
- 代码结构清晰，有适当的注释
- 用户界面友好

评分标准：
- 基础功能实现 (60分)
- 错误处理完善 (20分)  
- 代码结构清晰 (10分)
- 挑战功能实现 (10分)
"""

# 导入必要的库
import requests
import json
from datetime import datetime
import time
import os

class ISSTracker:
    """
    国际空间站追踪器类
    
    你需要实现以下方法：
    - __init__: 初始化方法
    - get_iss_position: 获取ISS位置
    - parse_data: 解析数据
    - display_info: 显示信息
    - save_data: 保存数据 (挑战)
    - track_movement: 追踪移动 (挑战)
    - calculate_speed: 计算速度 (挑战)
    - run: 主运行方法
    """
    
    def __init__(self):
        """
        初始化ISS追踪器
        
        TODO: 设置必要的属性
        - API URL
        - 数据存储列表
        - 其他需要的属性
        """
        pass
    
    def get_iss_position(self):
        """
        获取ISS当前位置
        
        TODO: 实现网络请求逻辑
        返回: response对象或None
        """
        pass
    
    def parse_data(self, response):
        """
        解析响应数据
        
        TODO: 实现数据解析逻辑
        参数: response - 网络响应对象
        返回: 解析后的数据字典或None
        """
        pass
    
    def display_info(self, data):
        """
        显示ISS信息
        
        TODO: 实现信息显示逻辑
        参数: data - 解析后的数据字典
        """
        pass
    
    def save_data(self, data):
        """
        保存数据到文件 (挑战功能)
        
        TODO: 将数据保存到CSV或JSON文件
        参数: data - 要保存的数据
        """
        pass
    
    def track_movement(self, times=3, interval=10):
        """
        追踪ISS移动 (挑战功能)
        
        TODO: 多次获取位置数据，显示移动轨迹
        参数: 
        - times: 获取次数
        - interval: 间隔时间(秒)
        """
        pass
    
    def calculate_speed(self, pos1, pos2, time_diff):
        """
        计算ISS移动速度 (挑战功能)
        
        TODO: 根据两个位置和时间差计算速度
        参数:
        - pos1: 第一个位置 (lat, lon)
        - pos2: 第二个位置 (lat, lon)  
        - time_diff: 时间差(秒)
        返回: 速度 (km/h)
        """
        pass
    
    def run(self):
        """
        主运行方法
        
        TODO: 实现主程序逻辑
        - 显示欢迎信息
        - 提供用户选择菜单
        - 调用相应的功能方法
        """
        pass

def main():
    """
    程序入口点
    """
    # TODO: 创建ISSTracker实例并运行
    pass

if __name__ == "__main__":
    main()
