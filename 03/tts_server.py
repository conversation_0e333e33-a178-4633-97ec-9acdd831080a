"""
TTS本地服务端
使用pyttsx3提供文字转语音服务

启动方法: python tts_server.py
服务地址: http://localhost:8000/tts
请求方法: POST
请求参数: {"text": "要转换的文字"}
返回: mp3格式音频文件
"""

import pyttsx3
import tempfile
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import threading
import time

class TTSHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        if self.path == '/tts':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8'))
                    text = data.get('text', '')
                except:
                    data = {}
                    if post_data:
                        params = post_data.decode('utf-8').split('&')
                        for param in params:
                            if '=' in param:
                                key, value = param.split('=', 1)
                                data[key] = value
                    text = data.get('text', '')
                
                if not text:
                    self.send_error(400, "Missing text parameter")
                    return
                
                temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
                temp_file.close()
                
                engine = pyttsx3.init()
                engine.save_to_file(text, temp_file.name)
                engine.runAndWait()
                
                if os.path.exists(temp_file.name):
                    self.send_response(200)
                    self.send_header('Content-Type', 'audio/wav')
                    self.send_header('Content-Disposition', 'attachment; filename="speech.wav"')
                    self.end_headers()
                    
                    with open(temp_file.name, 'rb') as f:
                        self.wfile.write(f.read())
                    
                    os.unlink(temp_file.name)
                else:
                    self.send_error(500, "Failed to generate audio")
                    
            except Exception as e:
                self.send_error(500, f"Server error: {str(e)}")
        else:
            self.send_error(404, "Not found")
    
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>TTS服务</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>TTS文字转语音服务</h1>
                <p>服务状态: 运行中</p>
                <p>使用方法:</p>
                <pre>
POST /tts
Content-Type: application/json

{"text": "你好世界"}
                </pre>
                <h2>测试工具</h2>
                <form id="ttsForm">
                    <textarea id="textInput" placeholder="输入要转换的文字" rows="4" cols="50"></textarea><br><br>
                    <button type="submit">转换为语音</button>
                </form>
                <div id="result"></div>
                
                <script>
                document.getElementById('ttsForm').onsubmit = function(e) {
                    e.preventDefault();
                    const text = document.getElementById('textInput').value;
                    if (!text) return;
                    
                    fetch('/tts', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({text: text})
                    })
                    .then(response => response.blob())
                    .then(blob => {
                        const url = URL.createObjectURL(blob);
                        const audio = new Audio(url);
                        audio.play();
                        document.getElementById('result').innerHTML = '语音已生成并播放';
                    })
                    .catch(err => {
                        document.getElementById('result').innerHTML = '错误: ' + err;
                    });
                };
                </script>
            </body>
            </html>
            """
            self.wfile.write(html.encode('utf-8'))
        else:
            self.send_error(404, "Not found")
    
    def log_message(self, format, *args):
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server():
    server_address = ('localhost', 8000)
    httpd = HTTPServer(server_address, TTSHandler)
    print("TTS服务已启动")
    print("服务地址: http://localhost:8000")
    print("API地址: http://localhost:8000/tts")
    print("按 Ctrl+C 停止服务")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务已停止")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
