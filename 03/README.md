# L3 - API综合应用

## 教学目标

本项目通过三个实用API学习Python网络编程进阶概念：

- **词典API** - JSON数据的逐层解析，列表和字典的判断处理
- **翻译API** - POST请求方法，表单数据提交，异常处理
- **TTS语音合成** - 二进制数据接收，文件操作，本地服务调用

## 文件说明

### 1. 分层次练习模板 (三个难度级别)

#### `practice_easy.py` - 简单级别
- **适合对象**: 有基础网络编程经验的学生
- **练习方式**: 填空练习，重点在关键概念
- **学习重点**: JSON逐层解析，POST请求，二进制数据处理
- **完成时间**: 20-25分钟

#### `practice_medium.py` - 中等级别
- **适合对象**: 熟悉Python基础语法和requests库
- **练习方式**: 半引导式，提供复杂逻辑框架
- **学习重点**: 异常处理，数据验证，完整功能实现
- **完成时间**: 30-40分钟

#### `practice_hard.py` - 困难级别
- **适合对象**: 有一定编程经验，追求挑战
- **练习方式**: 仅提供类框架和接口要求
- **学习重点**: 完整项目架构，错误处理，可选功能扩展
- **完成时间**: 50-70分钟

### 2. `practice_answers.py` - 练习答案
- 完整的参考实现
- 供教师参考和学生对照

### 3. `tts_server.py` - TTS本地服务端
- 基于pyttsx3的简单HTTP服务
- 接收POST请求，返回mp3音频文件
- 为TTS练习提供本地API支持

### 4. `auto_grader.py` - 自动评分
- **适用对象**: 困难级别练习 (`practice_hard.py`)
- **评分内容**: 8个维度，总分100分
- **功能特点**:
  - 自动测试三个API功能
  - 详细的评分报告和改进建议
  - 代码质量和结构评估
- **使用方法**: `python auto_grader.py`

## API说明

### 词典API
- **地址**: https://api.dictionaryapi.dev/api/v2/entries/en/<word>
- **方法**: GET
- **重点**: JSON数据的逐层提取，处理列表和字典嵌套结构

### 小牛翻译API
- **地址**: https://api.niutrans.com/v2/text/translate
- **方法**: POST
- **重点**: 表单数据提交，权限验证，异常处理

### TTS服务
- **地址**: http://localhost:8000/tts
- **方法**: POST
- **重点**: 二进制数据接收，文件保存和播放

## 使用步骤

1. 启动TTS服务: `python tts_server.py`
2. 选择对应难度的练习文件进行学习
3. 完成后可运行自动评分工具检验效果

## 注意事项

- 翻译API需要注册小牛翻译获取appId和密钥
- TTS服务需要先启动才能进行相关练习
- 建议按难度顺序逐步学习
