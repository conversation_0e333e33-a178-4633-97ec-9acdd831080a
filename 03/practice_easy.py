"""
练习模板 - 简单级别

学习重点：
- 词典API的JSON逐层解析
- POST请求和表单数据提交  
- 二进制数据接收和文件操作
"""

import requests
import json
import hashlib
import time

def lookup_word():
    """
    步骤1: 查询单词释义
    重点：JSON数据的逐层提取
    """
    print("=== 词典API练习 ===")
    
    word = "hello"
    # TODO: 构建API地址
    # 提示：https://api.dictionaryapi.dev/api/v2/entries/en/ + 单词
    url = "https://api.dictionaryapi.dev/api/v2/entries/en/" + _____
    
    response = requests.get(url)
    
    if response.status_code == 200:
        # TODO: 解析JSON数据
        # 提示：response.json()返回Python对象
        data = response._____()
        
        print(f"查询单词: {word}")
        
        # TODO: 判断data是否为列表
        # 提示：使用isinstance(data, list)
        if isinstance(data, _____):
            # TODO: 获取第一个词条
            # 提示：列表的第一个元素是data[0]
            word_data = data[_____]
            
            # TODO: 获取meanings字段
            # 提示：字典的键名是"meanings"
            meanings = word_data["_____"]
            
            # TODO: 遍历meanings列表
            # 提示：使用for循环
            for meaning in _____:
                # TODO: 获取词性
                # 提示：字典的键名是"partOfSpeech"
                part_of_speech = meaning["_____"]
                print(f"词性: {part_of_speech}")
                
                # TODO: 获取definitions字段
                # 提示：字典的键名是"definitions"
                definitions = meaning["_____"]
                
                # TODO: 获取第一个定义
                # 提示：列表的第一个元素
                first_def = definitions[_____]
                
                # TODO: 获取definition字段
                # 提示：字典的键名是"definition"
                definition = first_def["_____"]
                print(f"释义: {definition}")
                print()
    else:
        print("查询失败")

def translate_text():
    """
    步骤2: 翻译文本
    重点：POST请求和表单数据
    """
    print("=== 翻译API练习 ===")
    
    # 注意：这里使用模拟数据，实际使用需要注册获取真实的appId和密钥
    app_id = "demo_app_id"
    secret_key = "demo_secret_key"
    text = "Hello world"
    
    # TODO: 获取当前时间戳（毫秒）
    # 提示：int(time.time() * 1000)
    timestamp = int(time.time() * _____)
    
    # TODO: 构建权限字符串
    # 提示：app_id + secret_key + str(timestamp)
    auth_string = app_id + _____ + str(timestamp)
    
    # TODO: 计算MD5哈希
    # 提示：hashlib.md5(字符串.encode()).hexdigest()
    auth_str = hashlib.md5(auth_string._____())._____()
    
    # TODO: 构建POST数据
    # 提示：字典格式，包含所有必需字段
    data = {
        "from": "en",
        "to": "zh",
        "appId": _____,
        "srcText": _____,
        "timestamp": str(_____),
        "authStr": _____
    }
    
    url = "https://api.niutrans.com/v2/text/translate"
    
    # TODO: 发送POST请求
    # 提示：requests.post(url, data=data)
    response = requests._____(url, data=_____)
    
    print(f"原文: {text}")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        # TODO: 解析响应JSON
        result = response._____()
        
        # TODO: 获取翻译结果
        # 提示：字典的键名通常是"tgt_text"或"result"
        if "tgt_text" in result:
            translated = result["_____"]
            print(f"译文: {translated}")
        else:
            print("翻译响应格式:", result)
    else:
        print("翻译失败")

def text_to_speech():
    """
    步骤3: 文字转语音
    重点：二进制数据接收
    """
    print("=== TTS语音合成练习 ===")
    
    text = "你好，世界"
    
    # TODO: 构建请求数据
    # 提示：JSON格式，包含text字段
    data = {"_____": text}
    
    url = "http://localhost:8000/tts"
    
    try:
        # TODO: 发送POST请求，指定JSON格式
        # 提示：requests.post(url, json=data)
        response = requests._____(url, _____=data)
        
        if response.status_code == 200:
            print(f"转换文本: {text}")
            
            # TODO: 获取二进制内容
            # 提示：response.content包含二进制数据
            audio_data = response._____
            
            # TODO: 保存为文件
            # 提示：使用'wb'模式写入二进制数据
            with open("speech.wav", "_____") as f:
                f._____(audio_data)
            
            print("语音文件已保存为 speech.wav")
            print("可以使用音频播放器播放该文件")
        else:
            print(f"TTS请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到TTS服务")
        print("请先运行: python tts_server.py")

def main():
    """
    主程序：依次执行三个API练习
    """
    print("=" * 50)
    print("API综合练习 - 简单级别")
    print("=" * 50)
    
    lookup_word()
    print()
    
    translate_text()
    print()
    
    text_to_speech()
    
    print("\n练习完成！")

if __name__ == "__main__":
    main()

"""
答案提示：
1. word
2. json
3. list
4. 0 (出现3次)
5. meanings (出现2次)
6. meanings
7. partOfSpeech
8. definitions
9. definition
10. 1000
11. secret_key
12. encode
13. hexdigest
14. app_id
15. text
16. timestamp
17. auth_str
18. post
19. data
20. json
21. tgt_text
22. text
23. post
24. json
25. content
26. wb
27. write
"""
