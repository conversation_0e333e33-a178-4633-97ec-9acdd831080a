"""
自动测试评分系统
用于评估 practice_hard.py 的完成质量

评分标准：
- 词典API功能 (20分)
- 翻译API功能 (20分)  
- TTS功能 (20分)
- 错误处理 (15分)
- 代码结构 (15分)
- 可选功能 (10分)
"""

import importlib.util
import sys
import io
import contextlib
import traceback
import inspect
import requests
import json
import os
import time

class APIGrader:
    def __init__(self, student_file="practice_hard.py"):
        self.student_file = student_file
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
        self.student_module = None
        
    def load_student_code(self):
        """加载学生的代码文件"""
        try:
            spec = importlib.util.spec_from_file_location("student_code", self.student_file)
            self.student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.student_module)
            return True
        except Exception as e:
            self.add_result("代码加载", 0, 5, f"无法加载代码文件: {e}")
            return False
    
    def add_result(self, test_name, score, max_score, comment=""):
        """添加测试结果"""
        self.test_results.append({
            'name': test_name,
            'score': score,
            'max_score': max_score,
            'comment': comment
        })
        self.total_score += score
    
    def test_class_structure(self):
        """测试类结构完整性 (15分)"""
        required_classes = [
            'APIManager', 'DictionaryService', 'TranslationService', 
            'TTSService', 'LanguageLearningApp'
        ]
        
        score = 0
        missing_classes = []
        
        for class_name in required_classes:
            if hasattr(self.student_module, class_name):
                score += 3
            else:
                missing_classes.append(class_name)
        
        comment = f"缺少类: {', '.join(missing_classes)}" if missing_classes else "类结构完整"
        self.add_result("类结构", score, 15, comment)
    
    def test_dictionary_functionality(self):
        """测试词典API功能 (20分)"""
        try:
            if not hasattr(self.student_module, 'DictionaryService'):
                self.add_result("词典API", 0, 20, "缺少DictionaryService类")
                return
            
            dictionary = self.student_module.DictionaryService()
            
            # 测试基本方法存在
            methods = ['lookup_word', 'parse_meanings', 'format_output']
            method_score = 0
            
            for method in methods:
                if hasattr(dictionary, method):
                    method_score += 2
            
            # 测试功能实现
            function_score = 0
            try:
                # 模拟测试（避免实际API调用）
                if hasattr(dictionary, 'lookup_word'):
                    function_score += 7
                if hasattr(dictionary, 'parse_meanings'):
                    function_score += 4
                if hasattr(dictionary, 'format_output'):
                    function_score += 3
            except Exception as e:
                function_score = max(0, function_score - 5)
            
            total_score = method_score + function_score
            self.add_result("词典API", total_score, 20, f"方法完整性: {method_score}/6, 功能实现: {function_score}/14")
            
        except Exception as e:
            self.add_result("词典API", 0, 20, f"测试失败: {e}")
    
    def test_translation_functionality(self):
        """测试翻译API功能 (20分)"""
        try:
            if not hasattr(self.student_module, 'TranslationService'):
                self.add_result("翻译API", 0, 20, "缺少TranslationService类")
                return
            
            translator = self.student_module.TranslationService()
            
            methods = ['translate', 'generate_auth', 'get_supported_languages']
            method_score = 0
            
            for method in methods:
                if hasattr(translator, method):
                    method_score += 3
            
            # 测试初始化和属性
            init_score = 0
            if hasattr(translator, '__init__'):
                init_score += 5
            
            # 测试功能逻辑
            logic_score = 0
            try:
                if hasattr(translator, 'generate_auth'):
                    logic_score += 6
            except:
                pass
            
            total_score = method_score + init_score + logic_score
            self.add_result("翻译API", total_score, 20, f"方法: {method_score}/9, 初始化: {init_score}/5, 逻辑: {logic_score}/6")
            
        except Exception as e:
            self.add_result("翻译API", 0, 20, f"测试失败: {e}")
    
    def test_tts_functionality(self):
        """测试TTS功能 (20分)"""
        try:
            if not hasattr(self.student_module, 'TTSService'):
                self.add_result("TTS功能", 0, 20, "缺少TTSService类")
                return
            
            tts = self.student_module.TTSService()
            
            methods = ['text_to_speech', 'play_audio', 'check_server_status']
            method_score = 0
            
            for method in methods:
                if hasattr(tts, method):
                    method_score += 4
            
            # 测试属性和初始化
            attr_score = 0
            if hasattr(tts, 'server_url'):
                attr_score += 4
            if hasattr(tts, '__init__'):
                attr_score += 4
            
            total_score = method_score + attr_score
            self.add_result("TTS功能", total_score, 20, f"方法: {method_score}/12, 属性: {attr_score}/8")
            
        except Exception as e:
            self.add_result("TTS功能", 0, 20, f"测试失败: {e}")
    
    def test_error_handling(self):
        """测试错误处理 (15分)"""
        score = 0
        
        # 检查异常处理
        source_code = ""
        try:
            with open(self.student_file, 'r', encoding='utf-8') as f:
                source_code = f.read()
        except:
            pass
        
        error_patterns = [
            'try:', 'except', 'requests.exceptions', 
            'json.JSONDecodeError', 'ConnectionError'
        ]
        
        for pattern in error_patterns:
            if pattern in source_code:
                score += 3
        
        self.add_result("错误处理", min(score, 15), 15, f"发现 {min(len([p for p in error_patterns if p in source_code]), 5)} 种错误处理模式")
    
    def test_main_app_structure(self):
        """测试主应用结构 (15分)"""
        try:
            if not hasattr(self.student_module, 'LanguageLearningApp'):
                self.add_result("主应用结构", 0, 15, "缺少LanguageLearningApp类")
                return
            
            app = self.student_module.LanguageLearningApp()
            
            required_methods = [
                'run', 'show_menu', 'handle_user_input',
                'process_word_lookup', 'process_translation', 'process_tts'
            ]
            
            method_score = 0
            for method in required_methods:
                if hasattr(app, method):
                    method_score += 2
            
            # 检查初始化
            init_score = 0
            if hasattr(app, '__init__'):
                init_score += 3
            
            total_score = method_score + init_score
            self.add_result("主应用结构", total_score, 15, f"方法: {method_score}/12, 初始化: {init_score}/3")
            
        except Exception as e:
            self.add_result("主应用结构", 0, 15, f"测试失败: {e}")
    
    def test_optional_features(self):
        """测试可选功能 (10分)"""
        score = 0
        features_found = []
        
        optional_classes = ['HistoryManager', 'ConfigManager']
        
        for class_name in optional_classes:
            if hasattr(self.student_module, class_name):
                score += 5
                features_found.append(class_name)
        
        comment = f"实现的可选功能: {', '.join(features_found)}" if features_found else "未实现可选功能"
        self.add_result("可选功能", score, 10, comment)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始自动评分...")
        print("=" * 50)
        
        if not self.load_student_code():
            return
        
        # 运行各项测试
        self.test_class_structure()
        self.test_dictionary_functionality()
        self.test_translation_functionality()
        self.test_tts_functionality()
        self.test_error_handling()
        self.test_main_app_structure()
        self.test_optional_features()
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成评分报告"""
        print("\n" + "=" * 50)
        print("评分报告")
        print("=" * 50)
        
        for result in self.test_results:
            percentage = (result['score'] / result['max_score']) * 100
            status = "✓" if percentage >= 70 else "✗"
            print(f"{status} {result['name']}: {result['score']}/{result['max_score']} ({percentage:.1f}%)")
            if result['comment']:
                print(f"   {result['comment']}")
        
        print("\n" + "-" * 50)
        final_percentage = (self.total_score / self.max_score) * 100
        print(f"总分: {self.total_score}/{self.max_score} ({final_percentage:.1f}%)")
        
        # 等级评定
        if final_percentage >= 90:
            grade = "优秀 (A)"
        elif final_percentage >= 80:
            grade = "良好 (B)"
        elif final_percentage >= 70:
            grade = "及格 (C)"
        else:
            grade = "需要改进 (D)"
        
        print(f"等级: {grade}")
        
        # 改进建议
        print("\n改进建议:")
        if final_percentage < 100:
            low_scores = [r for r in self.test_results if (r['score'] / r['max_score']) < 0.7]
            for result in low_scores:
                print(f"- 加强 {result['name']} 的实现")
        else:
            print("- 代码实现完整，继续保持！")

def main():
    """主函数"""
    grader = APIGrader()
    grader.run_all_tests()

if __name__ == "__main__":
    main()
