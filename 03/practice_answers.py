"""
练习答案 - 完整实现

包含所有难度级别的完整答案
供教师参考和学生对照学习
"""

import requests
import json
import hashlib
import time
import os
import platform

# ===== 简单级别答案 =====

def lookup_word_easy():
    """简单级别 - 词典API答案"""
    print("=== 词典API练习 ===")
    
    word = "hello"
    url = "https://api.dictionaryapi.dev/api/v2/entries/en/" + word
    
    response = requests.get(url)
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"查询单词: {word}")
        
        if isinstance(data, list):
            word_data = data[0]
            meanings = word_data["meanings"]
            
            for meaning in meanings:
                part_of_speech = meaning["partOfSpeech"]
                print(f"词性: {part_of_speech}")
                
                definitions = meaning["definitions"]
                first_def = definitions[0]
                definition = first_def["definition"]
                print(f"释义: {definition}")
                print()
    else:
        print("查询失败")

def translate_text_easy():
    """简单级别 - 翻译API答案"""
    print("=== 翻译API练习 ===")
    
    app_id = "demo_app_id"
    secret_key = "demo_secret_key"
    text = "Hello world"
    
    timestamp = int(time.time() * 1000)
    auth_string = app_id + secret_key + str(timestamp)
    auth_str = hashlib.md5(auth_string.encode()).hexdigest()
    
    data = {
        "from": "en",
        "to": "zh",
        "appId": app_id,
        "srcText": text,
        "timestamp": str(timestamp),
        "authStr": auth_str
    }
    
    url = "https://api.niutrans.com/v2/text/translate"
    response = requests.post(url, data=data)
    
    print(f"原文: {text}")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if "tgt_text" in result:
            translated = result["tgt_text"]
            print(f"译文: {translated}")
        else:
            print("翻译响应格式:", result)
    else:
        print("翻译失败")

def text_to_speech_easy():
    """简单级别 - TTS答案"""
    print("=== TTS语音合成练习 ===")
    
    text = "你好，世界"
    data = {"text": text}
    url = "http://localhost:8000/tts"
    
    try:
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            print(f"转换文本: {text}")
            audio_data = response.content
            
            with open("speech.wav", "wb") as f:
                f.write(audio_data)
            
            print("语音文件已保存为 speech.wav")
            print("可以使用音频播放器播放该文件")
        else:
            print(f"TTS请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到TTS服务")
        print("请先运行: python tts_server.py")

# ===== 中等级别答案 =====

class DictionaryAPI:
    def __init__(self):
        self.base_url = "https://api.dictionaryapi.dev/api/v2/entries/en/"
    
    def lookup_word(self, word):
        try:
            url = self.base_url + word
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return True, data
            else:
                return False, f"API返回错误状态码: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            return False, f"网络请求失败: {e}"
        except json.JSONDecodeError:
            return False, "响应数据格式错误"
        except Exception as e:
            return False, f"未知错误: {e}"
    
    def extract_meanings(self, api_data):
        meanings_list = []
        
        try:
            if isinstance(api_data, list) and len(api_data) > 0:
                word_entry = api_data[0]
                meanings = word_entry.get("meanings", [])
                
                for meaning in meanings:
                    part_of_speech = meaning.get("partOfSpeech", "未知")
                    definitions = meaning.get("definitions", [])
                    
                    for definition in definitions:
                        meaning_info = {
                            "part_of_speech": part_of_speech,
                            "definition": definition.get("definition", ""),
                            "example": definition.get("example", "")
                        }
                        meanings_list.append(meaning_info)
            
            return meanings_list
            
        except Exception as e:
            print(f"数据提取错误: {e}")
            return []

class TranslatorAPI:
    def __init__(self, app_id, secret_key):
        self.app_id = app_id
        self.secret_key = secret_key
        self.base_url = "https://api.niutrans.com/v2/text/translate"
    
    def generate_auth_str(self, timestamp):
        auth_string = self.app_id + self.secret_key + str(timestamp)
        return hashlib.md5(auth_string.encode()).hexdigest()
    
    def translate(self, text, from_lang="en", to_lang="zh"):
        try:
            timestamp = int(time.time() * 1000)
            auth_str = self.generate_auth_str(timestamp)
            
            data = {
                "from": from_lang,
                "to": to_lang,
                "appId": self.app_id,
                "srcText": text,
                "timestamp": str(timestamp),
                "authStr": auth_str
            }
            
            response = requests.post(self.base_url, data=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if "tgt_text" in result:
                    return True, result["tgt_text"]
                else:
                    return False, "翻译结果格式错误"
            else:
                return False, f"翻译API返回错误: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            return False, f"网络请求失败: {e}"
        except Exception as e:
            return False, f"翻译失败: {e}"

class TTSClient:
    def __init__(self, server_url="http://localhost:8000/tts"):
        self.server_url = server_url
    
    def text_to_speech(self, text, output_file="speech.wav"):
        try:
            data = {"text": text}
            response = requests.post(self.server_url, json=data, timeout=30)
            
            if response.status_code == 200:
                with open(output_file, "wb") as f:
                    f.write(response.content)
                return True, f"语音文件已保存为 {output_file}"
            else:
                return False, f"TTS服务返回错误: {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            return False, "无法连接到TTS服务，请确保服务已启动"
        except Exception as e:
            return False, f"TTS转换失败: {e}"
    
    def play_audio(self, file_path):
        try:
            system = platform.system()
            
            if system == "Windows":
                os.system(f"start {file_path}")
            elif system == "Darwin":
                os.system(f"open {file_path}")
            else:
                os.system(f"xdg-open {file_path}")
                
            return True, "音频播放成功"
        except Exception as e:
            return False, f"播放失败: {e}"

def get_user_input():
    while True:
        word = input("请输入要查询的英文单词: ").strip()
        if word and word.isalpha():
            return word.lower()
        else:
            print("请输入有效的英文单词（只包含字母）")

def main_medium():
    """中等级别主程序"""
    print("=" * 50)
    print("API综合应用 - 中等级别")
    print("=" * 50)
    
    dictionary = DictionaryAPI()
    translator = TranslatorAPI("demo_app_id", "demo_secret_key")
    tts_client = TTSClient()
    
    word = get_user_input()
    
    success, result = dictionary.lookup_word(word)
    if success:
        meanings = dictionary.extract_meanings(result)
        if meanings:
            print(f"\n单词 '{word}' 的释义：")
            for i, meaning in enumerate(meanings[:3], 1):
                print(f"{i}. [{meaning['part_of_speech']}] {meaning['definition']}")
                if meaning['example']:
                    print(f"   例句: {meaning['example']}")
            
            translate_choice = input("\n是否需要翻译释义？(y/n): ").lower()
            if translate_choice == 'y':
                first_definition = meanings[0]['definition']
                success, translation = translator.translate(first_definition)
                if success:
                    print(f"翻译: {translation}")
                else:
                    print(f"翻译失败: {translation}")
            
            tts_choice = input("是否需要语音播放？(y/n): ").lower()
            if tts_choice == 'y':
                success, message = tts_client.text_to_speech(word)
                if success:
                    print(message)
                    tts_client.play_audio("speech.wav")
                else:
                    print(f"语音合成失败: {message}")
        else:
            print("未找到释义信息")
    else:
        print(f"查询失败: {result}")
    
    print("程序结束")

# ===== 困难级别答案 =====

class APIManager:
    def __init__(self):
        self.timeout = 10

    def make_request(self, url, method="GET", **kwargs):
        try:
            kwargs.setdefault('timeout', self.timeout)
            if method.upper() == "GET":
                return requests.get(url, **kwargs)
            elif method.upper() == "POST":
                return requests.post(url, **kwargs)
        except Exception as e:
            self.handle_error(e)
            return None

    def handle_error(self, error):
        print(f"API请求错误: {error}")

class DictionaryService(APIManager):
    def __init__(self):
        super().__init__()
        self.base_url = "https://api.dictionaryapi.dev/api/v2/entries/en/"

    def lookup_word(self, word):
        url = self.base_url + word
        response = self.make_request(url)

        if response and response.status_code == 200:
            try:
                data = response.json()
                meanings = self.parse_meanings(data)
                return True, meanings
            except json.JSONDecodeError:
                return False, "JSON解析失败"
        else:
            return False, "查询失败"

    def parse_meanings(self, data):
        meanings = []
        if isinstance(data, list) and data:
            word_data = data[0]
            for meaning in word_data.get("meanings", []):
                for definition in meaning.get("definitions", []):
                    meanings.append({
                        "part_of_speech": meaning.get("partOfSpeech", ""),
                        "definition": definition.get("definition", ""),
                        "example": definition.get("example", "")
                    })
        return meanings

    def format_output(self, meanings):
        output = []
        for i, meaning in enumerate(meanings, 1):
            line = f"{i}. [{meaning['part_of_speech']}] {meaning['definition']}"
            if meaning['example']:
                line += f"\n   例句: {meaning['example']}"
            output.append(line)
        return "\n".join(output)

class TranslationService(APIManager):
    def __init__(self, app_id=None, secret_key=None):
        super().__init__()
        self.app_id = app_id or "demo_app_id"
        self.secret_key = secret_key or "demo_secret_key"
        self.base_url = "https://api.niutrans.com/v2/text/translate"
        self.supported_langs = {
            "en": "英语", "zh": "中文", "ja": "日语",
            "ko": "韩语", "fr": "法语", "de": "德语"
        }

    def translate(self, text, from_lang="en", to_lang="zh"):
        timestamp = int(time.time() * 1000)
        auth_str = self.generate_auth(timestamp)

        data = {
            "from": from_lang,
            "to": to_lang,
            "appId": self.app_id,
            "srcText": text,
            "timestamp": str(timestamp),
            "authStr": auth_str
        }

        response = self.make_request(self.base_url, "POST", data=data)

        if response and response.status_code == 200:
            try:
                result = response.json()
                return True, result.get("tgt_text", "翻译结果为空")
            except json.JSONDecodeError:
                return False, "翻译结果解析失败"
        else:
            return False, "翻译请求失败"

    def generate_auth(self, timestamp):
        auth_string = self.app_id + self.secret_key + str(timestamp)
        return hashlib.md5(auth_string.encode()).hexdigest()

    def get_supported_languages(self):
        return self.supported_langs

class TTSService(APIManager):
    def __init__(self, server_url="http://localhost:8000/tts"):
        super().__init__()
        self.server_url = server_url

    def text_to_speech(self, text, output_file="speech.wav"):
        data = {"text": text}
        response = self.make_request(self.server_url, "POST", json=data)

        if response and response.status_code == 200:
            try:
                with open(output_file, "wb") as f:
                    f.write(response.content)
                return True, f"语音文件已保存: {output_file}"
            except Exception as e:
                return False, f"文件保存失败: {e}"
        else:
            return False, "TTS服务请求失败"

    def play_audio(self, file_path):
        try:
            system = platform.system()
            if system == "Windows":
                os.system(f"start {file_path}")
            elif system == "Darwin":
                os.system(f"open {file_path}")
            else:
                os.system(f"xdg-open {file_path}")
            return True, "播放成功"
        except Exception as e:
            return False, f"播放失败: {e}"

    def check_server_status(self):
        try:
            response = requests.get("http://localhost:8000/", timeout=5)
            return response.status_code == 200
        except:
            return False

class HistoryManager:
    def __init__(self, history_file="history.json"):
        self.history_file = history_file
        self.history = self.load_history()

    def save_record(self, record_type, data):
        record = {
            "type": record_type,
            "data": data,
            "timestamp": time.time()
        }
        self.history.append(record)
        self.save_to_file()

    def load_history(self):
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        return []

    def save_to_file(self):
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存历史记录失败: {e}")

    def show_history(self):
        if not self.history:
            print("暂无历史记录")
            return

        print("历史记录:")
        for i, record in enumerate(self.history[-10:], 1):
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S",
                                    time.localtime(record['timestamp']))
            print(f"{i}. [{record['type']}] {timestamp}")
            print(f"   {record['data']}")

    def clear_history(self):
        self.history = []
        self.save_to_file()
        print("历史记录已清空")

class ConfigManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        return {
            "app_id": "",
            "secret_key": "",
            "default_lang": "zh",
            "auto_play": False
        }

    def save_config(self):
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")

    def get_setting(self, key, default=None):
        return self.config.get(key, default)

    def set_setting(self, key, value):
        self.config[key] = value
        self.save_config()

class LanguageLearningApp:
    def __init__(self):
        self.config = ConfigManager()
        self.history = HistoryManager()
        self.dictionary = DictionaryService()
        self.translator = TranslationService(
            self.config.get_setting("app_id"),
            self.config.get_setting("secret_key")
        )
        self.tts = TTSService()
        self.running = True

    def run(self):
        print("=" * 50)
        print("语言学习应用 - 困难级别")
        print("=" * 50)

        while self.running:
            self.show_menu()
            choice = self.handle_user_input()

            if choice == "1":
                self.process_word_lookup()
            elif choice == "2":
                self.process_translation()
            elif choice == "3":
                self.process_tts()
            elif choice == "4":
                self.history.show_history()
            elif choice == "5":
                self.show_settings()
            elif choice == "0":
                self.running = False
                print("再见！")
            else:
                print("无效选择，请重试")

    def show_menu(self):
        print("\n主菜单:")
        print("1. 单词查询")
        print("2. 文本翻译")
        print("3. 语音合成")
        print("4. 历史记录")
        print("5. 设置")
        print("0. 退出")

    def handle_user_input(self):
        return input("请选择功能 (0-5): ").strip()

    def process_word_lookup(self):
        word = input("请输入要查询的单词: ").strip()
        if not word:
            return

        success, result = self.dictionary.lookup_word(word)
        if success:
            output = self.dictionary.format_output(result)
            print(f"\n单词 '{word}' 的释义:")
            print(output)
            self.history.save_record("查询", f"单词: {word}")
        else:
            print(f"查询失败: {result}")

    def process_translation(self):
        text = input("请输入要翻译的文本: ").strip()
        if not text:
            return

        success, result = self.translator.translate(text)
        if success:
            print(f"翻译结果: {result}")
            self.history.save_record("翻译", f"{text} -> {result}")
        else:
            print(f"翻译失败: {result}")

    def process_tts(self):
        text = input("请输入要转换为语音的文本: ").strip()
        if not text:
            return

        if not self.tts.check_server_status():
            print("TTS服务未启动，请先运行 python tts_server.py")
            return

        success, result = self.tts.text_to_speech(text)
        if success:
            print(result)
            if self.config.get_setting("auto_play", False):
                self.tts.play_audio("speech.wav")
            self.history.save_record("TTS", text)
        else:
            print(f"语音合成失败: {result}")

    def show_settings(self):
        print("\n当前设置:")
        print(f"APP ID: {self.config.get_setting('app_id', '未设置')}")
        print(f"默认语言: {self.config.get_setting('default_lang')}")
        print(f"自动播放: {self.config.get_setting('auto_play')}")

def main_hard():
    """困难级别主程序"""
    app = LanguageLearningApp()
    app.run()

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 简单级别演示")
    print("2. 中等级别演示")
    print("3. 困难级别演示")

    choice = input("请选择 (1/2/3): ")

    if choice == "1":
        lookup_word_easy()
        print()
        translate_text_easy()
        print()
        text_to_speech_easy()
    elif choice == "2":
        main_medium()
    elif choice == "3":
        main_hard()
    else:
        print("无效选择")
