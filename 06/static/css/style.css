* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
}

.container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.login-box {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    min-width: 300px;
}

.login-box h1 {
    margin-bottom: 20px;
    color: #333;
}

.login-box input {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.login-box button {
    width: 100%;
    padding: 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
}

.login-box button:hover {
    background-color: #0056b3;
}

.header {
    background-color: #333;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-container {
    display: flex;
    height: calc(100vh - 60px);
    flex-direction: column;
}

.toolbar {
    background-color: #444;
    padding: 10px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.content-area {
    display: flex;
    flex: 1;
    min-height: 0;
}

.toolbar button {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.toolbar button:hover {
    background-color: #0056b3;
}

.workspace {
    flex: 1;
    background-color: white;
    position: relative;
    min-width: 0;
}

.canvas {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: auto;
}

.device {
    position: absolute;
    width: 80px;
    height: 60px;
    border: 2px solid #333;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: move;
    user-select: none;
    font-size: 12px;
    text-align: center;
}

.device.router {
    background-color: #ffeb3b;
}

.device.client {
    background-color: #4caf50;
    color: white;
}

.device.selected {
    border-color: #ff5722;
    box-shadow: 0 0 10px rgba(255, 87, 34, 0.5);
}

.sidebar {
    width: 320px;
    min-width: 320px;
    background-color: #f8f9fa;
    padding: 20px;
    overflow-y: auto;
}

.sidebar h3 {
    margin-bottom: 10px;
    color: #333;
}

.sidebar > div {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.sidebar input, .sidebar select {
    width: 100%;
    padding: 8px;
    margin: 5px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.sidebar button {
    width: 100%;
    padding: 10px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.sidebar button:hover {
    background-color: #218838;
}

#commandOutput {
    background-color: #000;
    color: #0f0;
    padding: 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    min-height: 100px;
    margin-top: 10px;
    white-space: pre-wrap;
}

#deviceDetails {
    background-color: white;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    min-height: 80px;
    font-size: 14px;
}

.validation-panel {
    margin-top: 20px;
}

#validationResults {
    background-color: white;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    min-height: 60px;
    font-size: 14px;
    max-height: 200px;
    overflow-y: auto;
}

#validationResults ul {
    margin: 5px 0;
    padding-left: 20px;
}

#validationResults li {
    margin: 3px 0;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #333;
}

.modal-content button {
    margin-top: 20px;
    padding: 10px 20px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.modal-content button:hover {
    background: #5a6268;
}

.connection-line {
    position: absolute;
    background-color: #333;
    height: 2px;
    transform-origin: left center;
    z-index: 1;
    pointer-events: none;
}
