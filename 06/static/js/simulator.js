let devices = {};
let selectedDevice = null;
let draggedDevice = null;
let dragOffset = { x: 0, y: 0 };
let connectionMode = false;
let connectionDevices = [];
let connections = [];

async function addDevice(type) {
    const name = prompt(`输入${type === 'router' ? '路由器' : '客户端'}名称:`);
    if (!name) return;
    
    const response = await fetch('/api/add_device', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: type,
            name: name,
            x: Math.random() * 400 + 50,
            y: Math.random() * 300 + 50
        })
    });
    
    const result = await response.json();
    if (result.success) {
        createDeviceElement(result.device);
        updateDeviceSelect();
    }
}

function createDeviceElement(device) {
    const canvas = document.getElementById('canvas');
    const deviceEl = document.createElement('div');
    deviceEl.className = `device ${device.type}`;
    deviceEl.id = `device-${device.id}`;
    deviceEl.style.left = device.x + 'px';
    deviceEl.style.top = device.y + 'px';
    deviceEl.innerHTML = `<div>${device.name}</div><div>${device.type}</div>`;
    
    deviceEl.addEventListener('click', () => selectDevice(device.id));
    deviceEl.addEventListener('mousedown', (e) => startDrag(e, device.id));
    
    canvas.appendChild(deviceEl);
    devices[device.id] = device;
}

function selectDevice(deviceId) {
    if (connectionMode) {
        handleConnectionModeClick(deviceId);
        return;
    }

    if (selectedDevice) {
        document.getElementById(`device-${selectedDevice}`).classList.remove('selected');
    }

    selectedDevice = deviceId;
    document.getElementById(`device-${deviceId}`).classList.add('selected');

    const device = devices[deviceId];
    let interfaceInfo = '';
    if (device.interfaces && Object.keys(device.interfaces).length > 0) {
        interfaceInfo = '<br><strong>接口:</strong><br>';
        for (const [iface, config] of Object.entries(device.interfaces)) {
            interfaceInfo += `${iface}: ${config.ip}/${config.mask}<br>`;
        }
    }

    document.getElementById('deviceDetails').innerHTML = `
        <strong>名称:</strong> ${device.name}<br>
        <strong>类型:</strong> ${device.type}<br>
        <strong>ID:</strong> ${device.id}${interfaceInfo}
    `;
}

function startDrag(e, deviceId) {
    draggedDevice = deviceId;
    const deviceEl = document.getElementById(`device-${deviceId}`);
    const rect = deviceEl.getBoundingClientRect();
    const canvasRect = document.getElementById('canvas').getBoundingClientRect();
    
    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;
    
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);
    e.preventDefault();
}

function drag(e) {
    if (!draggedDevice) return;
    
    const canvas = document.getElementById('canvas');
    const canvasRect = canvas.getBoundingClientRect();
    const deviceEl = document.getElementById(`device-${draggedDevice}`);
    
    const x = e.clientX - canvasRect.left - dragOffset.x;
    const y = e.clientY - canvasRect.top - dragOffset.y;
    
    deviceEl.style.left = Math.max(0, Math.min(x, canvasRect.width - 80)) + 'px';
    deviceEl.style.top = Math.max(0, Math.min(y, canvasRect.height - 60)) + 'px';
}

function stopDrag() {
    draggedDevice = null;
    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDrag);
}

async function executeCommand() {
    const deviceId = document.getElementById('deviceSelect').value;
    const command = document.getElementById('commandInput').value;
    
    if (!deviceId || !command) {
        alert('请选择设备并输入命令');
        return;
    }
    
    const response = await fetch('/api/execute_command', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId,
            command: command
        })
    });
    
    const result = await response.json();
    if (result.success) {
        document.getElementById('commandOutput').textContent = result.output;
    } else {
        document.getElementById('commandOutput').textContent = 'Error: ' + result.error;
    }
    
    document.getElementById('commandInput').value = '';
}

async function configureInterface() {
    const deviceId = document.getElementById('deviceSelect').value;
    const interfaceName = document.getElementById('interfaceName').value;
    const ipAddress = document.getElementById('ipAddress').value;
    const subnetMask = document.getElementById('subnetMask').value;
    
    if (!deviceId || !interfaceName || !ipAddress || !subnetMask) {
        alert('请填写所有字段');
        return;
    }
    
    const response = await fetch('/api/configure_interface', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId,
            interface: interfaceName,
            ip_address: ipAddress,
            subnet_mask: subnetMask
        })
    });
    
    const result = await response.json();
    if (result.success) {
        alert('接口配置成功');
        document.getElementById('interfaceName').value = '';
        document.getElementById('ipAddress').value = '';
        document.getElementById('subnetMask').value = '';
    } else {
        alert('配置失败: ' + result.error);
    }
}

function updateDeviceSelect() {
    const select = document.getElementById('deviceSelect');
    select.innerHTML = '<option value="">选择设备</option>';
    
    for (const deviceId in devices) {
        const device = devices[deviceId];
        const option = document.createElement('option');
        option.value = deviceId;
        option.textContent = `${device.name} (${device.type})`;
        select.appendChild(option);
    }
}

function clearAll() {
    if (confirm('确定要清空所有设备吗？')) {
        const canvas = document.getElementById('canvas');
        canvas.innerHTML = '';
        devices = {};
        connections = [];
        selectedDevice = null;
        connectionMode = false;
        connectionDevices = [];
        updateDeviceSelect();
        updateConnectionList();
        document.getElementById('deviceDetails').textContent = '选择一个设备查看详情';
        document.getElementById('commandOutput').textContent = '';
        document.getElementById('connectionMode').style.display = 'none';
        canvas.style.cursor = 'default';
    }
}

document.getElementById('commandInput').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        executeCommand();
    }
});

async function loadTopology() {
    const response = await fetch('/api/topology');
    const topology = await response.json();

    if (topology.devices) {
        for (const deviceId in topology.devices) {
            const device = topology.devices[deviceId];
            createDeviceElement(device);
        }
        updateDeviceSelect();

        if (topology.connections) {
            setTimeout(() => {
                topology.connections.forEach(conn => {
                    drawConnection(conn.device1, conn.device2);
                });
                updateConnectionList();
            }, 100);
        }
    }
}

async function showTemplates() {
    const response = await fetch('/api/templates');
    const data = await response.json();

    const templateList = document.getElementById('templateList');
    templateList.innerHTML = '';

    for (const templateName of data.templates) {
        const template = data.details[templateName];
        const div = document.createElement('div');
        div.style.cssText = 'margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;';
        div.innerHTML = `
            <h4>${template.name}</h4>
            <p>设备数量: ${template.devices.length}</p>
            <button onclick="applyTemplate('${templateName}')" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">应用模板</button>
        `;
        templateList.appendChild(div);
    }

    document.getElementById('templateModal').style.display = 'block';
}

async function applyTemplate(templateName) {
    const response = await fetch(`/api/template/${templateName}/apply`, {
        method: 'POST'
    });

    const result = await response.json();
    if (result.success) {
        alert(result.message);
        closeTemplateModal();
        loadTopology();
    } else {
        alert('应用模板失败: ' + result.message);
    }
}

function closeTemplateModal() {
    document.getElementById('templateModal').style.display = 'none';
}

async function showScenarios() {
    const response = await fetch('/api/scenarios');
    const scenarios = await response.json();

    const scenarioList = document.getElementById('scenarioList');
    scenarioList.innerHTML = '';

    for (const [level, scenario] of Object.entries(scenarios)) {
        const div = document.createElement('div');
        div.style.cssText = 'margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;';
        div.innerHTML = `
            <h4>${scenario.name}</h4>
            <p>${scenario.description}</p>
            <p><strong>任务:</strong></p>
            <ul>${scenario.tasks.map(task => `<li>${task}</li>`).join('')}</ul>
            <p><strong>最低分数:</strong> ${scenario.min_score}</p>
            <button onclick="checkScenario('${level}')" style="padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">检查完成度</button>
        `;
        scenarioList.appendChild(div);
    }

    document.getElementById('scenarioModal').style.display = 'block';
}

async function checkScenario(level) {
    const response = await fetch(`/api/scenario/${level}/check`);
    const result = await response.json();

    if (result.success) {
        alert('场景完成: ' + result.message);
    } else {
        alert('场景未完成: ' + result.message);
    }
}

function closeScenarioModal() {
    document.getElementById('scenarioModal').style.display = 'none';
}

function showConnectionMode() {
    connectionMode = true;
    connectionDevices = [];
    document.getElementById('connectionMode').style.display = 'block';

    const canvas = document.getElementById('canvas');
    canvas.style.cursor = 'crosshair';
}

function exitConnectionMode() {
    connectionMode = false;
    connectionDevices = [];
    document.getElementById('connectionMode').style.display = 'none';

    const canvas = document.getElementById('canvas');
    canvas.style.cursor = 'default';
}

function handleConnectionModeClick(deviceId) {
    if (connectionDevices.includes(deviceId)) {
        return;
    }

    connectionDevices.push(deviceId);
    const deviceEl = document.getElementById(`device-${deviceId}`);
    deviceEl.style.border = '3px solid #ff5722';

    if (connectionDevices.length === 2) {
        showConnectionModal();
    }
}

function showConnectionModal() {
    const device1 = devices[connectionDevices[0]];
    const device2 = devices[connectionDevices[1]];

    document.getElementById('device1Name').textContent = device1.name;
    document.getElementById('device2Name').textContent = device2.name;
    document.getElementById('interface1Name').value = '';
    document.getElementById('interface2Name').value = '';

    document.getElementById('connectionModal').style.display = 'block';
}

function closeConnectionModal() {
    document.getElementById('connectionModal').style.display = 'none';

    connectionDevices.forEach(deviceId => {
        const deviceEl = document.getElementById(`device-${deviceId}`);
        deviceEl.style.border = '2px solid #333';
    });

    connectionDevices = [];
}

async function createConnection() {
    const device1Id = connectionDevices[0];
    const device2Id = connectionDevices[1];
    const interface1 = document.getElementById('interface1Name').value;
    const interface2 = document.getElementById('interface2Name').value;

    if (!interface1 || !interface2) {
        alert('请填写接口名称');
        return;
    }

    const response = await fetch('/api/connect_devices', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device1_id: device1Id,
            device2_id: device2Id,
            interface1: interface1,
            interface2: interface2
        })
    });

    const result = await response.json();
    if (result.success) {
        alert('连接创建成功');
        closeConnectionModal();
        exitConnectionMode();
        drawConnection(device1Id, device2Id);
        updateConnectionList();
    } else {
        alert('连接创建失败: ' + result.error);
    }
}

function drawConnection(device1Id, device2Id) {
    const device1El = document.getElementById(`device-${device1Id}`);
    const device2El = document.getElementById(`device-${device2Id}`);
    const canvas = document.getElementById('canvas');

    const rect1 = device1El.getBoundingClientRect();
    const rect2 = device2El.getBoundingClientRect();
    const canvasRect = canvas.getBoundingClientRect();

    const x1 = rect1.left - canvasRect.left + rect1.width / 2;
    const y1 = rect1.top - canvasRect.top + rect1.height / 2;
    const x2 = rect2.left - canvasRect.left + rect2.width / 2;
    const y2 = rect2.top - canvasRect.top + rect2.height / 2;

    const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

    const line = document.createElement('div');
    line.className = 'connection-line';
    line.id = `connection-${device1Id}-${device2Id}`;
    line.style.left = x1 + 'px';
    line.style.top = y1 + 'px';
    line.style.width = length + 'px';
    line.style.transform = `rotate(${angle}deg)`;

    canvas.appendChild(line);

    connections.push({
        device1: device1Id,
        device2: device2Id,
        element: line
    });
}

function updateConnectionList() {
    const connectionList = document.getElementById('connectionList');
    connectionList.innerHTML = '<h4>当前连接:</h4>';

    connections.forEach(conn => {
        const device1 = devices[conn.device1];
        const device2 = devices[conn.device2];
        const div = document.createElement('div');
        div.style.cssText = 'margin: 5px 0; padding: 5px; background: #e9ecef; border-radius: 3px; font-size: 12px;';
        div.textContent = `${device1.name} ↔ ${device2.name}`;
        connectionList.appendChild(div);
    });
}

async function validateNetwork() {
    const response = await fetch('/api/validate');
    const result = await response.json();

    const validationResults = document.getElementById('validationResults');

    let html = `<p><strong>网络${result.valid ? '有效' : '无效'}</strong></p>`;

    if (result.issues.length > 0) {
        html += '<p><strong>问题:</strong></p><ul>';
        result.issues.forEach(issue => {
            html += `<li style="color: red;">${issue}</li>`;
        });
        html += '</ul>';
    }

    if (result.warnings.length > 0) {
        html += '<p><strong>警告:</strong></p><ul>';
        result.warnings.forEach(warning => {
            html += `<li style="color: orange;">${warning}</li>`;
        });
        html += '</ul>';
    }

    if (result.valid && result.warnings.length === 0) {
        html += '<p style="color: green;">网络配置良好!</p>';
    }

    validationResults.innerHTML = html;
}

window.addEventListener('load', loadTopology);
