let devices = {};
let selectedDevice = null;
let draggedDevice = null;
let dragOffset = { x: 0, y: 0 };
let connectionMode = false;
let connectionDevices = [];
let connections = [];
let svgElement = null;
let isDraggingConnection = false;
let dragStartPort = null;
let tempLine = null;

async function addDevice(type) {
    const name = prompt(`输入${type === 'router' ? '路由器' : '客户端'}名称:`);
    if (!name) return;
    
    const response = await fetch('/api/add_device', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: type,
            name: name,
            x: Math.random() * 400 + 50,
            y: Math.random() * 300 + 50
        })
    });
    
    const result = await response.json();
    if (result.success) {
        createDeviceElement(result.device);
        updateDeviceSelect();
    }
}

function createDeviceElement(device) {
    const canvas = document.getElementById('canvas');
    const deviceEl = document.createElement('div');
    deviceEl.className = `device ${device.type}`;
    deviceEl.id = `device-${device.id}`;
    deviceEl.style.left = device.x + 'px';
    deviceEl.style.top = device.y + 'px';
    deviceEl.innerHTML = `<div>${device.name}</div><div>${device.type}</div>`;

    deviceEl.addEventListener('click', () => selectDevice(device.id));
    deviceEl.addEventListener('mousedown', (e) => startDrag(e, device.id));

    canvas.appendChild(deviceEl);
    devices[device.id] = device;

    createDevicePorts(device.id);
}

function createDevicePorts(deviceId) {
    const deviceEl = document.getElementById(`device-${deviceId}`);
    const device = devices[deviceId];

    // 根据设备类型确定默认端口数量
    const defaultPortCount = device.type === 'router' ? 4 : 1;

    // 初始化设备的端口数据
    if (!device.ports) {
        device.ports = [];
        for (let i = 0; i < defaultPortCount; i++) {
            device.ports.push({
                id: `port${i}`,
                name: `端口${i + 1}`,
                connected: false
            });
        }
    }

    updateDevicePorts(deviceId);
}

function updateDevicePorts(deviceId) {
    const deviceEl = document.getElementById(`device-${deviceId}`);
    const device = devices[deviceId];

    // 清除现有端口
    const existingPorts = deviceEl.querySelectorAll('.device-port');
    existingPorts.forEach(port => port.remove());

    // 重新创建端口
    device.ports.forEach((port, index) => {
        const position = getPortPosition(index, device.ports.length);

        const portEl = document.createElement('div');
        portEl.className = 'device-port';
        portEl.id = `port-${deviceId}-${port.id}`;
        portEl.style.left = position.x + 'px';
        portEl.style.top = position.y + 'px';
        portEl.title = `${device.name} - ${port.name}`;

        if (port.connected) {
            portEl.classList.add('connected');
        }

        portEl.addEventListener('mousedown', (e) => startConnectionDrag(e, deviceId, port.id));
        portEl.addEventListener('mouseup', (e) => endConnectionDrag(e, deviceId, port.id));
        portEl.addEventListener('mouseenter', (e) => highlightPort(e, deviceId, port.id));
        portEl.addEventListener('mouseleave', (e) => unhighlightPort(e, deviceId, port.id));

        deviceEl.appendChild(portEl);
    });

    // 添加"添加端口"按钮（仅对路由器）
    if (device.type === 'router') {
        const addPortBtn = document.createElement('div');
        addPortBtn.className = 'add-port-btn';
        addPortBtn.innerHTML = '+';
        addPortBtn.style.position = 'absolute';
        addPortBtn.style.right = '-15px';
        addPortBtn.style.top = '-15px';
        addPortBtn.style.width = '20px';
        addPortBtn.style.height = '20px';
        addPortBtn.style.backgroundColor = '#28a745';
        addPortBtn.style.color = 'white';
        addPortBtn.style.borderRadius = '50%';
        addPortBtn.style.display = 'flex';
        addPortBtn.style.alignItems = 'center';
        addPortBtn.style.justifyContent = 'center';
        addPortBtn.style.cursor = 'pointer';
        addPortBtn.style.fontSize = '12px';
        addPortBtn.style.zIndex = '15';
        addPortBtn.title = '添加端口';

        addPortBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            addPort(deviceId);
        });

        deviceEl.appendChild(addPortBtn);
    }
}

function getPortPosition(index, totalPorts) {
    const deviceWidth = 80;
    const deviceHeight = 60;
    const portRadius = 4;

    if (totalPorts === 1) {
        return { x: deviceWidth - portRadius, y: deviceHeight / 2 - portRadius };
    }

    // 将端口分布在设备周围
    const positions = [];

    // 右侧端口
    const rightPorts = Math.ceil(totalPorts / 4);
    for (let i = 0; i < rightPorts && positions.length < totalPorts; i++) {
        positions.push({
            x: deviceWidth - portRadius,
            y: (deviceHeight / (rightPorts + 1)) * (i + 1) - portRadius
        });
    }

    // 底部端口
    const bottomPorts = Math.ceil((totalPorts - positions.length) / 3);
    for (let i = 0; i < bottomPorts && positions.length < totalPorts; i++) {
        positions.push({
            x: (deviceWidth / (bottomPorts + 1)) * (i + 1) - portRadius,
            y: deviceHeight - portRadius
        });
    }

    // 左侧端口
    const leftPorts = Math.ceil((totalPorts - positions.length) / 2);
    for (let i = 0; i < leftPorts && positions.length < totalPorts; i++) {
        positions.push({
            x: -portRadius,
            y: (deviceHeight / (leftPorts + 1)) * (i + 1) - portRadius
        });
    }

    // 顶部端口
    const topPorts = totalPorts - positions.length;
    for (let i = 0; i < topPorts; i++) {
        positions.push({
            x: (deviceWidth / (topPorts + 1)) * (i + 1) - portRadius,
            y: -portRadius
        });
    }

    return positions[index] || { x: deviceWidth - portRadius, y: deviceHeight / 2 - portRadius };
}

function addPort(deviceId) {
    const device = devices[deviceId];
    const newPortIndex = device.ports.length;

    device.ports.push({
        id: `port${newPortIndex}`,
        name: `端口${newPortIndex + 1}`,
        connected: false
    });

    updateDevicePorts(deviceId);
}

function selectDevice(deviceId) {
    if (connectionMode) {
        handleConnectionModeClick(deviceId);
        return;
    }

    if (selectedDevice) {
        document.getElementById(`device-${selectedDevice}`).classList.remove('selected');
    }

    selectedDevice = deviceId;
    document.getElementById(`device-${deviceId}`).classList.add('selected');

    const device = devices[deviceId];
    let interfaceInfo = '';
    if (device.interfaces && Object.keys(device.interfaces).length > 0) {
        interfaceInfo = '<br><strong>接口:</strong><br>';
        for (const [iface, config] of Object.entries(device.interfaces)) {
            interfaceInfo += `${iface}: ${config.ip}/${config.mask}<br>`;
        }
    }

    document.getElementById('deviceDetails').innerHTML = `
        <strong>名称:</strong> ${device.name}<br>
        <strong>类型:</strong> ${device.type}<br>
        <strong>ID:</strong> ${device.id}${interfaceInfo}
    `;

    // 自动设置命令执行的设备
    const deviceSelect = document.getElementById('deviceSelect');
    deviceSelect.value = deviceId;

    // 更新接口配置面板
    updateInterfaceConfigPanel(deviceId);
}

function updateInterfaceConfigPanel(deviceId) {
    const device = devices[deviceId];
    if (!device) return;

    // 更新接口选择下拉框
    const interfaceSelect = document.getElementById('interfaceSelect');
    if (!interfaceSelect) {
        // 如果不存在，创建接口选择下拉框
        const interfaceName = document.getElementById('interfaceName');
        if (interfaceName) {
            const select = document.createElement('select');
            select.id = 'interfaceSelect';
            select.style.cssText = interfaceName.style.cssText;

            // 添加端口选项
            if (device.ports) {
                device.ports.forEach(port => {
                    const option = document.createElement('option');
                    option.value = port.id;
                    option.textContent = port.name;
                    select.appendChild(option);
                });
            }

            // 添加现有接口选项
            if (device.interfaces) {
                Object.keys(device.interfaces).forEach(iface => {
                    const option = document.createElement('option');
                    option.value = iface;
                    option.textContent = iface;
                    select.appendChild(option);
                });
            }

            select.addEventListener('change', () => {
                updateInterfaceFields();
            });

            interfaceName.parentNode.replaceChild(select, interfaceName);
        }
    } else {
        // 更新现有的选择框
        interfaceSelect.innerHTML = '';

        // 添加端口选项
        if (device.ports) {
            device.ports.forEach(port => {
                const option = document.createElement('option');
                option.value = port.id;
                option.textContent = port.name;
                interfaceSelect.appendChild(option);
            });
        }

        // 添加现有接口选项
        if (device.interfaces) {
            Object.keys(device.interfaces).forEach(iface => {
                const option = document.createElement('option');
                option.value = iface;
                option.textContent = iface;
                interfaceSelect.appendChild(option);
            });
        }
    }

    // 更新IP地址和子网掩码字段
    updateInterfaceFields();
}

function updateInterfaceFields() {
    const deviceId = selectedDevice;
    const device = devices[deviceId];
    if (!device) return;

    const interfaceSelect = document.getElementById('interfaceSelect');
    const ipAddress = document.getElementById('ipAddress');
    const subnetMask = document.getElementById('subnetMask');

    if (!interfaceSelect || !ipAddress || !subnetMask) return;

    const selectedInterface = interfaceSelect.value;

    if (device.interfaces && device.interfaces[selectedInterface]) {
        const config = device.interfaces[selectedInterface];
        ipAddress.value = config.ip || '';
        subnetMask.value = config.mask || '';
    } else {
        ipAddress.value = '';
        subnetMask.value = '';
    }
}

function startDrag(e, deviceId) {
    if (isDraggingConnection) return;

    // 检查是否点击的是端口或添加按钮
    if (e.target.classList.contains('device-port') ||
        e.target.classList.contains('add-port-btn')) {
        return;
    }

    draggedDevice = deviceId;
    const deviceEl = document.getElementById(`device-${deviceId}`);
    const rect = deviceEl.getBoundingClientRect();
    const canvasRect = document.getElementById('canvas').getBoundingClientRect();

    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;

    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);
    e.preventDefault();
}

function drag(e) {
    if (!draggedDevice) return;
    
    const canvas = document.getElementById('canvas');
    const canvasRect = canvas.getBoundingClientRect();
    const deviceEl = document.getElementById(`device-${draggedDevice}`);
    
    const x = e.clientX - canvasRect.left - dragOffset.x;
    const y = e.clientY - canvasRect.top - dragOffset.y;
    
    deviceEl.style.left = Math.max(0, Math.min(x, canvasRect.width - 80)) + 'px';
    deviceEl.style.top = Math.max(0, Math.min(y, canvasRect.height - 60)) + 'px';
}

function stopDrag() {
    if (draggedDevice) {
        updateAllConnections();
    }
    draggedDevice = null;
    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDrag);
}

async function executeCommand() {
    const deviceId = document.getElementById('deviceSelect').value;
    const command = document.getElementById('commandInput').value;
    
    if (!deviceId || !command) {
        alert('请选择设备并输入命令');
        return;
    }
    
    const response = await fetch('/api/execute_command', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId,
            command: command
        })
    });
    
    const result = await response.json();
    if (result.success) {
        document.getElementById('commandOutput').textContent = result.output;
    } else {
        document.getElementById('commandOutput').textContent = 'Error: ' + result.error;
    }
    
    document.getElementById('commandInput').value = '';
}

async function configureInterface() {
    const deviceId = document.getElementById('deviceSelect').value;
    const interfaceSelect = document.getElementById('interfaceSelect');
    const interfaceName = interfaceSelect ? interfaceSelect.value : document.getElementById('interfaceName').value;
    const ipAddress = document.getElementById('ipAddress').value;
    const subnetMask = document.getElementById('subnetMask').value;

    if (!deviceId || !interfaceName || !ipAddress || !subnetMask) {
        alert('请填写所有字段');
        return;
    }

    const response = await fetch('/api/configure_interface', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId,
            interface: interfaceName,
            ip_address: ipAddress,
            subnet_mask: subnetMask
        })
    });

    const result = await response.json();
    if (result.success) {
        alert('接口配置成功');

        // 更新设备数据
        const device = devices[deviceId];
        if (device) {
            if (!device.interfaces) device.interfaces = {};
            device.interfaces[interfaceName] = {
                ip: ipAddress,
                mask: subnetMask,
                status: 'up'
            };
        }

        // 刷新设备详情显示
        if (selectedDevice === deviceId) {
            selectDevice(deviceId);
        }

        // 清空输入字段
        document.getElementById('ipAddress').value = '';
        document.getElementById('subnetMask').value = '';
    } else {
        alert('配置失败: ' + result.error);
    }
}

function updateDeviceSelect() {
    const select = document.getElementById('deviceSelect');
    select.innerHTML = '<option value="">选择设备</option>';
    
    for (const deviceId in devices) {
        const device = devices[deviceId];
        const option = document.createElement('option');
        option.value = deviceId;
        option.textContent = `${device.name} (${device.type})`;
        select.appendChild(option);
    }
}

function clearAll(skipConfirm = false) {
    if (!skipConfirm && !confirm('确定要清空所有设备吗？')) {
        return;
    }

    const canvas = document.getElementById('canvas');
    canvas.innerHTML = '';
    devices = {};
    connections = [];
    selectedDevice = null;
    connectionMode = false;
    connectionDevices = [];
    svgElement = null;
    isDraggingConnection = false;
    dragStartPort = null;
    tempLine = null;
    updateDeviceSelect();
    updateConnectionList();
    document.getElementById('deviceDetails').textContent = '选择一个设备查看详情';
    document.getElementById('commandOutput').textContent = '';
    if (document.getElementById('connectionMode')) {
        document.getElementById('connectionMode').style.display = 'none';
    }
    canvas.style.cursor = 'default';
}

document.getElementById('commandInput').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        executeCommand();
    }
});

async function loadTopology() {
    const response = await fetch('/api/topology');
    const topology = await response.json();

    if (topology.devices) {
        for (const deviceId in topology.devices) {
            const device = topology.devices[deviceId];
            createDeviceElement(device);
        }
        updateDeviceSelect();

        if (topology.connections) {
            setTimeout(() => {
                initializeSVG();
                topology.connections.forEach(conn => {
                    // 为模板连接找到合适的端口
                    const device1 = devices[conn.device1];
                    const device2 = devices[conn.device2];

                    let port1Id = 'port0';
                    let port2Id = 'port0';

                    // 为设备找到可用的端口
                    if (device1 && device1.ports) {
                        const availablePort1 = device1.ports.find(p => !p.connected);
                        if (availablePort1) {
                            port1Id = availablePort1.id;
                            availablePort1.connected = true;
                        }
                    }

                    if (device2 && device2.ports) {
                        const availablePort2 = device2.ports.find(p => !p.connected);
                        if (availablePort2) {
                            port2Id = availablePort2.id;
                            availablePort2.connected = true;
                        }
                    }

                    drawConnection(conn.device1, conn.device2, port1Id, port2Id);

                    // 更新端口显示
                    updateDevicePorts(conn.device1);
                    updateDevicePorts(conn.device2);
                });
                updateConnectionList();
            }, 100);
        }
    }
}

async function showTemplates() {
    const response = await fetch('/api/templates');
    const data = await response.json();

    const templateList = document.getElementById('templateList');
    templateList.innerHTML = '';

    for (const templateName of data.templates) {
        const template = data.details[templateName];
        const div = document.createElement('div');
        div.style.cssText = 'margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;';
        div.innerHTML = `
            <h4>${template.name}</h4>
            <p>设备数量: ${template.devices.length}</p>
            <button onclick="applyTemplate('${templateName}')" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">应用模板</button>
        `;
        templateList.appendChild(div);
    }

    document.getElementById('templateModal').style.display = 'block';
}

async function applyTemplate(templateName) {
    if (Object.keys(devices).length > 0) {
        if (!confirm('应用模板将清空当前网络，是否继续？')) {
            return;
        }
        clearAll(true);
    }

    const response = await fetch(`/api/template/${templateName}/apply`, {
        method: 'POST'
    });

    const result = await response.json();
    if (result.success) {
        alert(result.message);
        closeTemplateModal();
        loadTopology();
    } else {
        alert('应用模板失败: ' + result.message);
    }
}

function closeTemplateModal() {
    document.getElementById('templateModal').style.display = 'none';
}

async function showScenarios() {
    const response = await fetch('/api/scenarios');
    const scenarios = await response.json();

    const scenarioList = document.getElementById('scenarioList');
    scenarioList.innerHTML = '';

    for (const [level, scenario] of Object.entries(scenarios)) {
        const div = document.createElement('div');
        div.style.cssText = 'margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;';
        div.innerHTML = `
            <h4>${scenario.name}</h4>
            <p>${scenario.description}</p>
            <p><strong>任务:</strong></p>
            <ul>${scenario.tasks.map(task => `<li>${task}</li>`).join('')}</ul>
            <p><strong>最低分数:</strong> ${scenario.min_score}</p>
            <button onclick="checkScenario('${level}')" style="padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">检查完成度</button>
        `;
        scenarioList.appendChild(div);
    }

    document.getElementById('scenarioModal').style.display = 'block';
}

async function checkScenario(level) {
    const response = await fetch(`/api/scenario/${level}/check`);
    const result = await response.json();

    if (result.success) {
        alert('场景完成: ' + result.message);
    } else {
        alert('场景未完成: ' + result.message);
    }
}

function closeScenarioModal() {
    document.getElementById('scenarioModal').style.display = 'none';
}

function showConnectionMode() {
    connectionMode = true;
    connectionDevices = [];
    document.getElementById('connectionMode').style.display = 'block';

    const canvas = document.getElementById('canvas');
    canvas.style.cursor = 'crosshair';
}

function exitConnectionMode() {
    connectionMode = false;
    connectionDevices = [];
    document.getElementById('connectionMode').style.display = 'none';

    const canvas = document.getElementById('canvas');
    canvas.style.cursor = 'default';
}

function handleConnectionModeClick(deviceId) {
    if (connectionDevices.includes(deviceId)) {
        return;
    }

    connectionDevices.push(deviceId);
    const deviceEl = document.getElementById(`device-${deviceId}`);
    deviceEl.style.border = '3px solid #ff5722';

    if (connectionDevices.length === 2) {
        showConnectionModal();
    }
}

function showConnectionModal() {
    const device1 = devices[connectionDevices[0]];
    const device2 = devices[connectionDevices[1]];

    document.getElementById('device1Name').textContent = device1.name;
    document.getElementById('device2Name').textContent = device2.name;
    document.getElementById('interface1Name').value = '';
    document.getElementById('interface2Name').value = '';

    document.getElementById('connectionModal').style.display = 'block';
}

function closeConnectionModal() {
    document.getElementById('connectionModal').style.display = 'none';

    connectionDevices.forEach(deviceId => {
        const deviceEl = document.getElementById(`device-${deviceId}`);
        deviceEl.style.border = '2px solid #333';
    });

    connectionDevices = [];
}

async function createConnection() {
    const device1Id = connectionDevices[0];
    const device2Id = connectionDevices[1];
    const interface1 = document.getElementById('interface1Name').value;
    const interface2 = document.getElementById('interface2Name').value;

    if (!interface1 || !interface2) {
        alert('请填写接口名称');
        return;
    }

    const response = await fetch('/api/connect_devices', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device1_id: device1Id,
            device2_id: device2Id,
            interface1: interface1,
            interface2: interface2
        })
    });

    const result = await response.json();
    if (result.success) {
        alert('连接创建成功');
        closeConnectionModal();
        exitConnectionMode();
        drawConnection(device1Id, device2Id);
        updateConnectionList();
    } else {
        alert('连接创建失败: ' + result.error);
    }
}

function initializeSVG() {
    const canvas = document.getElementById('canvas');
    if (!svgElement) {
        svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svgElement.setAttribute('width', '100%');
        svgElement.setAttribute('height', '100%');
        svgElement.style.position = 'absolute';
        svgElement.style.top = '0';
        svgElement.style.left = '0';
        svgElement.style.pointerEvents = 'auto';
        svgElement.style.zIndex = '1';
        canvas.appendChild(svgElement);
        console.log('SVG initialized');
    }
}

function drawConnection(device1Id, device2Id, interface1 = 'center', interface2 = 'center') {
    initializeSVG();

    const device1El = document.getElementById(`device-${device1Id}`);
    const device2El = document.getElementById(`device-${device2Id}`);

    if (!device1El || !device2El) {
        console.error('Device elements not found:', device1Id, device2Id);
        return;
    }

    const pos1 = getDevicePortPosition(device1Id, interface1);
    const pos2 = getDevicePortPosition(device2Id, interface2);

    console.log('Drawing connection from', pos1, 'to', pos2);

    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    line.setAttribute('x1', pos1.x);
    line.setAttribute('y1', pos1.y);
    line.setAttribute('x2', pos2.x);
    line.setAttribute('y2', pos2.y);
    line.setAttribute('stroke', '#333333');
    line.setAttribute('stroke-width', '2');
    line.setAttribute('fill', 'none');
    line.style.cursor = 'pointer';
    line.id = `connection-${device1Id}-${device2Id}`;

    line.addEventListener('click', (e) => {
        e.stopPropagation();
        if (confirm('断开这个连接？')) {
            disconnectDevices(device1Id, device2Id, interface1, interface2);
        }
    });

    line.addEventListener('mouseenter', () => {
        line.setAttribute('stroke', '#ff5722');
        line.setAttribute('stroke-width', '3');
    });

    line.addEventListener('mouseleave', () => {
        line.setAttribute('stroke', '#333333');
        line.setAttribute('stroke-width', '2');
    });

    svgElement.appendChild(line);

    connections.push({
        device1: device1Id,
        device2: device2Id,
        interface1: interface1,
        interface2: interface2,
        element: line
    });

    console.log('Connection drawn successfully');
}

function getDevicePortPosition(deviceId, portId = 'center') {
    const deviceEl = document.getElementById(`device-${deviceId}`);
    if (!deviceEl) return { x: 0, y: 0 };

    const canvas = document.getElementById('canvas');
    const canvasRect = canvas.getBoundingClientRect();
    const deviceRect = deviceEl.getBoundingClientRect();

    if (portId === 'center') {
        return {
            x: deviceRect.left - canvasRect.left + 40,
            y: deviceRect.top - canvasRect.top + 30
        };
    }

    const portEl = document.getElementById(`port-${deviceId}-${portId}`);
    if (!portEl) {
        return {
            x: deviceRect.left - canvasRect.left + 40,
            y: deviceRect.top - canvasRect.top + 30
        };
    }

    const portRect = portEl.getBoundingClientRect();

    return {
        x: portRect.left - canvasRect.left + 4, // 端口中心
        y: portRect.top - canvasRect.top + 4   // 端口中心
    };
}

function updateAllConnections() {
    connections.forEach(conn => {
        const pos1 = getDevicePortPosition(conn.device1, conn.interface1);
        const pos2 = getDevicePortPosition(conn.device2, conn.interface2);

        conn.element.setAttribute('x1', pos1.x);
        conn.element.setAttribute('y1', pos1.y);
        conn.element.setAttribute('x2', pos2.x);
        conn.element.setAttribute('y2', pos2.y);
    });
}

function removeConnection(device1Id, device2Id) {
    const index = connections.findIndex(conn =>
        (conn.device1 === device1Id && conn.device2 === device2Id) ||
        (conn.device1 === device2Id && conn.device2 === device1Id)
    );

    if (index !== -1) {
        connections[index].element.remove();
        connections.splice(index, 1);
        updateConnectionList();
    }
}

async function disconnectDevices(device1Id, device2Id, interface1, interface2) {
    console.log('Disconnecting devices:', device1Id, device2Id);

    // 调用后端API断开连接
    const response = await fetch('/api/disconnect_devices', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device1_id: device1Id,
            device2_id: device2Id,
            interface1: interface1,
            interface2: interface2
        })
    });

    const result = await response.json();
    if (result.success) {
        // 移除连接线
        removeConnection(device1Id, device2Id);

        // 更新端口状态
        const device1 = devices[device1Id];
        const device2 = devices[device2Id];

        if (device1 && device1.ports) {
            const port1 = device1.ports.find(p => p.id === interface1);
            if (port1) port1.connected = false;
            updateDevicePorts(device1Id);
        }

        if (device2 && device2.ports) {
            const port2 = device2.ports.find(p => p.id === interface2);
            if (port2) port2.connected = false;
            updateDevicePorts(device2Id);
        }

        console.log('Devices disconnected successfully');
    } else {
        alert('断开连接失败: ' + result.error);
        console.error('Disconnect failed:', result.error);
    }
}

function startConnectionDrag(e, deviceId, portId) {
    e.stopPropagation();
    e.preventDefault();

    console.log('Starting connection drag from', deviceId, portId);

    isDraggingConnection = true;
    dragStartPort = { deviceId, portId };

    initializeSVG();

    const startPos = getDevicePortPosition(deviceId, portId);

    tempLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    tempLine.setAttribute('x1', startPos.x);
    tempLine.setAttribute('y1', startPos.y);
    tempLine.setAttribute('x2', startPos.x);
    tempLine.setAttribute('y2', startPos.y);
    tempLine.className = 'drag-line';

    svgElement.appendChild(tempLine);

    document.addEventListener('mousemove', dragConnection);
    document.addEventListener('mouseup', stopConnectionDrag);
}

function dragConnection(e) {
    if (!tempLine || !isDraggingConnection) return;

    const canvas = document.getElementById('canvas');
    const canvasRect = canvas.getBoundingClientRect();

    const x = e.clientX - canvasRect.left;
    const y = e.clientY - canvasRect.top;

    tempLine.setAttribute('x2', x);
    tempLine.setAttribute('y2', y);
}

function endConnectionDrag(e, deviceId, portId) {
    if (!isDraggingConnection || !dragStartPort) return;

    e.stopPropagation();
    e.preventDefault();

    console.log('Ending connection drag at', deviceId, portId);

    if (dragStartPort.deviceId !== deviceId) {
        createConnectionBetweenPorts(
            dragStartPort.deviceId,
            dragStartPort.portId,
            deviceId,
            portId
        );
    }

    stopConnectionDrag();
}

function stopConnectionDrag() {
    isDraggingConnection = false;
    dragStartPort = null;

    if (tempLine) {
        tempLine.remove();
        tempLine = null;
    }

    document.removeEventListener('mousemove', dragConnection);
    document.removeEventListener('mouseup', stopConnectionDrag);
}

function highlightPort(e, deviceId, portId) {
    if (isDraggingConnection && dragStartPort && dragStartPort.deviceId !== deviceId) {
        e.target.style.backgroundColor = '#ffc107';
    }
}

function unhighlightPort(e, deviceId, portId) {
    if (!e.target.classList.contains('connected')) {
        e.target.style.backgroundColor = '#007bff';
    }
}

async function createConnectionBetweenPorts(device1Id, port1Id, device2Id, port2Id) {
    console.log('Creating connection between', device1Id, port1Id, 'and', device2Id, port2Id);

    const interface1 = `${port1Id}`;
    const interface2 = `${port2Id}`;

    const response = await fetch('/api/connect_devices', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device1_id: device1Id,
            device2_id: device2Id,
            interface1: interface1,
            interface2: interface2
        })
    });

    const result = await response.json();
    if (result.success) {
        drawConnection(device1Id, device2Id, port1Id, port2Id);
        updateConnectionList();

        // 标记端口为已连接
        const device1 = devices[device1Id];
        const device2 = devices[device2Id];

        const port1 = device1.ports.find(p => p.id === port1Id);
        const port2 = device2.ports.find(p => p.id === port2Id);

        if (port1) port1.connected = true;
        if (port2) port2.connected = true;

        updateDevicePorts(device1Id);
        updateDevicePorts(device2Id);

        console.log('Connection created successfully');
    } else {
        alert('连接创建失败: ' + result.error);
        console.error('Connection failed:', result.error);
    }
}

function updateConnectionList() {
    const connectionList = document.getElementById('connectionList');
    connectionList.innerHTML = '<h4>当前连接:</h4>';

    connections.forEach(conn => {
        const device1 = devices[conn.device1];
        const device2 = devices[conn.device2];
        const div = document.createElement('div');
        div.style.cssText = 'margin: 5px 0; padding: 5px; background: #e9ecef; border-radius: 3px; font-size: 12px; cursor: pointer;';
        div.innerHTML = `${device1.name} (${conn.interface1}) ↔ ${device2.name} (${conn.interface2}) <span style="color: red; float: right;">×</span>`;
        div.addEventListener('click', () => {
            if (confirm('删除这个连接？')) {
                removeConnection(conn.device1, conn.device2);
            }
        });
        connectionList.appendChild(div);
    });
}

async function validateNetwork() {
    const response = await fetch('/api/validate');
    const result = await response.json();

    const validationResults = document.getElementById('validationResults');

    let html = `<p><strong>网络${result.valid ? '有效' : '无效'}</strong></p>`;

    if (result.issues.length > 0) {
        html += '<p><strong>问题:</strong></p><ul>';
        result.issues.forEach(issue => {
            html += `<li style="color: red;">${issue}</li>`;
        });
        html += '</ul>';
    }

    if (result.warnings.length > 0) {
        html += '<p><strong>警告:</strong></p><ul>';
        result.warnings.forEach(warning => {
            html += `<li style="color: orange;">${warning}</li>`;
        });
        html += '</ul>';
    }

    if (result.valid && result.warnings.length === 0) {
        html += '<p style="color: green;">网络配置良好!</p>';
    }

    validationResults.innerHTML = html;
}

window.addEventListener('load', loadTopology);
