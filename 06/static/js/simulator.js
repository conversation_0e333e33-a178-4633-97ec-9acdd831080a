let devices = {};
let selectedDevice = null;
let draggedDevice = null;
let dragOffset = { x: 0, y: 0 };

async function addDevice(type) {
    const name = prompt(`输入${type === 'router' ? '路由器' : '客户端'}名称:`);
    if (!name) return;
    
    const response = await fetch('/api/add_device', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: type,
            name: name,
            x: Math.random() * 400 + 50,
            y: Math.random() * 300 + 50
        })
    });
    
    const result = await response.json();
    if (result.success) {
        createDeviceElement(result.device);
        updateDeviceSelect();
    }
}

function createDeviceElement(device) {
    const canvas = document.getElementById('canvas');
    const deviceEl = document.createElement('div');
    deviceEl.className = `device ${device.type}`;
    deviceEl.id = `device-${device.id}`;
    deviceEl.style.left = device.x + 'px';
    deviceEl.style.top = device.y + 'px';
    deviceEl.innerHTML = `<div>${device.name}</div><div>${device.type}</div>`;
    
    deviceEl.addEventListener('click', () => selectDevice(device.id));
    deviceEl.addEventListener('mousedown', (e) => startDrag(e, device.id));
    
    canvas.appendChild(deviceEl);
    devices[device.id] = device;
}

function selectDevice(deviceId) {
    if (selectedDevice) {
        document.getElementById(`device-${selectedDevice}`).classList.remove('selected');
    }
    
    selectedDevice = deviceId;
    document.getElementById(`device-${deviceId}`).classList.add('selected');
    
    const device = devices[deviceId];
    document.getElementById('deviceDetails').innerHTML = `
        <strong>名称:</strong> ${device.name}<br>
        <strong>类型:</strong> ${device.type}<br>
        <strong>ID:</strong> ${device.id}
    `;
}

function startDrag(e, deviceId) {
    draggedDevice = deviceId;
    const deviceEl = document.getElementById(`device-${deviceId}`);
    const rect = deviceEl.getBoundingClientRect();
    const canvasRect = document.getElementById('canvas').getBoundingClientRect();
    
    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;
    
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);
    e.preventDefault();
}

function drag(e) {
    if (!draggedDevice) return;
    
    const canvas = document.getElementById('canvas');
    const canvasRect = canvas.getBoundingClientRect();
    const deviceEl = document.getElementById(`device-${draggedDevice}`);
    
    const x = e.clientX - canvasRect.left - dragOffset.x;
    const y = e.clientY - canvasRect.top - dragOffset.y;
    
    deviceEl.style.left = Math.max(0, Math.min(x, canvasRect.width - 80)) + 'px';
    deviceEl.style.top = Math.max(0, Math.min(y, canvasRect.height - 60)) + 'px';
}

function stopDrag() {
    draggedDevice = null;
    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDrag);
}

async function executeCommand() {
    const deviceId = document.getElementById('deviceSelect').value;
    const command = document.getElementById('commandInput').value;
    
    if (!deviceId || !command) {
        alert('请选择设备并输入命令');
        return;
    }
    
    const response = await fetch('/api/execute_command', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId,
            command: command
        })
    });
    
    const result = await response.json();
    if (result.success) {
        document.getElementById('commandOutput').textContent = result.output;
    } else {
        document.getElementById('commandOutput').textContent = 'Error: ' + result.error;
    }
    
    document.getElementById('commandInput').value = '';
}

async function configureInterface() {
    const deviceId = document.getElementById('deviceSelect').value;
    const interfaceName = document.getElementById('interfaceName').value;
    const ipAddress = document.getElementById('ipAddress').value;
    const subnetMask = document.getElementById('subnetMask').value;
    
    if (!deviceId || !interfaceName || !ipAddress || !subnetMask) {
        alert('请填写所有字段');
        return;
    }
    
    const response = await fetch('/api/configure_interface', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_id: deviceId,
            interface: interfaceName,
            ip_address: ipAddress,
            subnet_mask: subnetMask
        })
    });
    
    const result = await response.json();
    if (result.success) {
        alert('接口配置成功');
        document.getElementById('interfaceName').value = '';
        document.getElementById('ipAddress').value = '';
        document.getElementById('subnetMask').value = '';
    } else {
        alert('配置失败: ' + result.error);
    }
}

function updateDeviceSelect() {
    const select = document.getElementById('deviceSelect');
    select.innerHTML = '<option value="">选择设备</option>';
    
    for (const deviceId in devices) {
        const device = devices[deviceId];
        const option = document.createElement('option');
        option.value = deviceId;
        option.textContent = `${device.name} (${device.type})`;
        select.appendChild(option);
    }
}

function clearAll() {
    if (confirm('确定要清空所有设备吗？')) {
        const canvas = document.getElementById('canvas');
        canvas.innerHTML = '';
        devices = {};
        selectedDevice = null;
        updateDeviceSelect();
        document.getElementById('deviceDetails').textContent = '选择一个设备查看详情';
        document.getElementById('commandOutput').textContent = '';
    }
}

document.getElementById('commandInput').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        executeCommand();
    }
});

async function loadTopology() {
    const response = await fetch('/api/topology');
    const topology = await response.json();
    
    if (topology.devices) {
        for (const deviceId in topology.devices) {
            const device = topology.devices[deviceId];
            createDeviceElement(device);
        }
        updateDeviceSelect();
    }
}

window.addEventListener('load', loadTopology);
