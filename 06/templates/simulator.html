<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络模拟器 - {{ username }}</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="header">
        <h1>网络模拟器</h1>
        <span>用户: {{ username }}</span>
    </div>
    
    <div class="main-container">
        <div class="toolbar">
            <button onclick="addDevice('router')">添加路由器</button>
            <button onclick="addDevice('client')">添加客户端</button>
            <button onclick="clearAll()">清空</button>
            <button onclick="showTemplates()">使用模板</button>
            <button onclick="showScenarios()">练习场景</button>
            <button onclick="validateNetwork()">验证网络</button>
        </div>
        
        <div class="workspace">
            <div id="canvas" class="canvas"></div>
        </div>
        
        <div class="sidebar">
            <div class="device-info">
                <h3>设备信息</h3>
                <div id="deviceDetails">选择一个设备查看详情</div>
            </div>
            
            <div class="command-panel">
                <h3>命令执行</h3>
                <select id="deviceSelect">
                    <option value="">选择设备</option>
                </select>
                <input type="text" id="commandInput" placeholder="输入命令 (如: ip a, ip route)">
                <button onclick="executeCommand()">执行</button>
                <div id="commandOutput"></div>
            </div>
            
            <div class="config-panel">
                <h3>接口配置</h3>
                <input type="text" id="interfaceName" placeholder="接口名 (如: eth0)">
                <input type="text" id="ipAddress" placeholder="IP地址">
                <input type="text" id="subnetMask" placeholder="子网掩码">
                <button onclick="configureInterface()">配置接口</button>
            </div>

            <div class="validation-panel">
                <h3>网络验证</h3>
                <div id="validationResults"></div>
            </div>
        </div>
    </div>

    <div id="templateModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 500px; width: 90%;">
            <h3>选择网络模板</h3>
            <div id="templateList"></div>
            <button onclick="closeTemplateModal()" style="margin-top: 20px; padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">关闭</button>
        </div>
    </div>

    <div id="scenarioModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 600px; width: 90%;">
            <h3>练习场景</h3>
            <div id="scenarioList"></div>
            <button onclick="closeScenarioModal()" style="margin-top: 20px; padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">关闭</button>
        </div>
    </div>

    <script src="/static/js/simulator.js"></script>
</body>
</html>
