<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络模拟器 - {{ username }}</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="header">
        <h1>网络模拟器</h1>
        <span>用户: {{ username }}</span>
    </div>
    
    <div class="main-container">
        <div class="toolbar">
            <button onclick="addDevice('router')">添加路由器</button>
            <button onclick="addDevice('client')">添加客户端</button>
            <button onclick="clearAll()">清空</button>
            <button onclick="showTemplates()">使用模板</button>
            <button onclick="showScenarios()">练习场景</button>
            <button onclick="validateNetwork()">验证网络</button>
            <button onclick="showConnectionMode()">连接设备</button>
        </div>

        <div class="content-area">
            <div class="workspace">
                <div id="canvas" class="canvas"></div>
            </div>

            <div class="sidebar">
            <div class="device-info">
                <h3>设备信息</h3>
                <div id="deviceDetails">选择一个设备查看详情</div>
            </div>
            
            <div class="command-panel">
                <h3>命令执行</h3>
                <select id="deviceSelect">
                    <option value="">选择设备</option>
                </select>
                <input type="text" id="commandInput" placeholder="输入命令 (如: ip a, ip route)">
                <button onclick="executeCommand()">执行</button>
                <div id="commandOutput"></div>
            </div>
            
            <div class="config-panel">
                <h3>接口配置</h3>
                <input type="text" id="interfaceName" placeholder="接口名 (如: eth0)">
                <input type="text" id="ipAddress" placeholder="IP地址">
                <input type="text" id="subnetMask" placeholder="子网掩码">
                <button onclick="configureInterface()">配置接口</button>
            </div>

            <div class="validation-panel">
                <h3>网络验证</h3>
                <div id="validationResults"></div>
            </div>

            <div class="connection-panel">
                <h3>设备连接</h3>
                <div id="connectionMode" style="display: none;">
                    <p>连接模式已激活，点击两个设备进行连接</p>
                    <button onclick="exitConnectionMode()">退出连接模式</button>
                </div>
                <div id="connectionList"></div>
            </div>
        </div>
        </div>
    </div>

    <div id="templateModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>选择网络模板</h3>
            <div id="templateList"></div>
            <button onclick="closeTemplateModal()">关闭</button>
        </div>
    </div>

    <div id="scenarioModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>练习场景</h3>
            <div id="scenarioList"></div>
            <button onclick="closeScenarioModal()">关闭</button>
        </div>
    </div>

    <div id="connectionModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>创建连接</h3>
            <div id="connectionForm">
                <p>设备1: <span id="device1Name"></span></p>
                <p>设备2: <span id="device2Name"></span></p>
                <label>设备1接口名:</label>
                <input type="text" id="interface1Name" placeholder="如: eth0">
                <label>设备2接口名:</label>
                <input type="text" id="interface2Name" placeholder="如: eth0">
                <button onclick="createConnection()">创建连接</button>
                <button onclick="closeConnectionModal()">取消</button>
            </div>
        </div>
    </div>

    <script src="/static/js/simulator.js"></script>
</body>
</html>
