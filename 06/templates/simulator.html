<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络模拟器 - {{ username }}</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="header">
        <h1>网络模拟器</h1>
        <span>用户: {{ username }}</span>
    </div>
    
    <div class="main-container">
        <div class="toolbar">
            <button onclick="addDevice('router')">添加路由器</button>
            <button onclick="addDevice('client')">添加客户端</button>
            <button onclick="clearAll()">清空</button>
        </div>
        
        <div class="workspace">
            <div id="canvas" class="canvas"></div>
        </div>
        
        <div class="sidebar">
            <div class="device-info">
                <h3>设备信息</h3>
                <div id="deviceDetails">选择一个设备查看详情</div>
            </div>
            
            <div class="command-panel">
                <h3>命令执行</h3>
                <select id="deviceSelect">
                    <option value="">选择设备</option>
                </select>
                <input type="text" id="commandInput" placeholder="输入命令 (如: ip a, ip route)">
                <button onclick="executeCommand()">执行</button>
                <div id="commandOutput"></div>
            </div>
            
            <div class="config-panel">
                <h3>接口配置</h3>
                <input type="text" id="interfaceName" placeholder="接口名 (如: eth0)">
                <input type="text" id="ipAddress" placeholder="IP地址">
                <input type="text" id="subnetMask" placeholder="子网掩码">
                <button onclick="configureInterface()">配置接口</button>
            </div>
        </div>
    </div>

    <script src="/static/js/simulator.js"></script>
</body>
</html>
