from flask import Flask, render_template, request, jsonify, session, redirect, Response
import uuid
import json
import threading
import time
from datetime import datetime
from auto_grader import NetworkGrader, TeacherDashboard
from practice_scenarios import PracticeScenarios, NetworkTemplates, NetworkValidator

app = Flask(__name__)
app.secret_key = 'network_sim_secret_key'

class NetworkDevice:
    def __init__(self, device_type, name, x=0, y=0):
        self.id = str(uuid.uuid4())
        self.type = device_type
        self.name = name
        self.x = x
        self.y = y
        self.interfaces = {}
        self.routing_table = []
        self.connected_devices = {}
        
    def add_interface(self, interface_name, ip_address, subnet_mask):
        self.interfaces[interface_name] = {
            'ip': ip_address,
            'mask': subnet_mask,
            'status': 'up'
        }
        
    def connect_to(self, other_device, interface1, interface2):
        self.connected_devices[interface1] = {
            'device_id': other_device.id,
            'interface': interface2
        }
        other_device.connected_devices[interface2] = {
            'device_id': self.id,
            'interface': interface1
        }
        
    def execute_command(self, command):
        if command == 'ip a':
            return self._show_interfaces()
        elif command == 'ip route':
            return self._show_routing_table()
        elif command.startswith('ping '):
            target = command.split(' ')[1]
            return self._ping(target)
        else:
            return f"Command '{command}' not recognized"
            
    def _show_interfaces(self):
        result = []
        for iface, config in self.interfaces.items():
            result.append(f"{iface}: {config['ip']}/{config['mask']} {config['status']}")
        return '\n'.join(result) if result else "No interfaces configured"
        
    def _show_routing_table(self):
        if not self.routing_table:
            return "No routes configured"
        result = []
        for route in self.routing_table:
            result.append(f"{route['destination']} via {route['gateway']} dev {route['interface']}")
        return '\n'.join(result)
        
    def _ping(self, target):
        return f"PING {target}: 64 bytes from {target}: icmp_seq=1 time=1.2ms"

class NetworkEnvironment:
    def __init__(self, user_id):
        self.user_id = user_id
        self.devices = {}
        self.connections = []
        self.created_at = datetime.now()
        
    def add_device(self, device_type, name, x=0, y=0):
        device = NetworkDevice(device_type, name, x, y)
        self.devices[device.id] = device
        return device
        
    def remove_device(self, device_id):
        if device_id in self.devices:
            del self.devices[device_id]
            self.connections = [conn for conn in self.connections 
                             if conn['device1'] != device_id and conn['device2'] != device_id]
            
    def connect_devices(self, device1_id, device2_id, interface1, interface2):
        if device1_id in self.devices and device2_id in self.devices:
            device1 = self.devices[device1_id]
            device2 = self.devices[device2_id]
            device1.connect_to(device2, interface1, interface2)
            self.connections.append({
                'device1': device1_id,
                'device2': device2_id,
                'interface1': interface1,
                'interface2': interface2
            })
            
    def get_topology(self):
        return {
            'devices': {dev_id: {
                'id': dev.id,
                'type': dev.type,
                'name': dev.name,
                'x': dev.x,
                'y': dev.y,
                'interfaces': dev.interfaces
            } for dev_id, dev in self.devices.items()},
            'connections': self.connections
        }

user_environments = {}
teacher_dashboard_instance = TeacherDashboard(user_environments)
practice_scenarios = PracticeScenarios()
network_templates = NetworkTemplates()
network_validator = NetworkValidator()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['POST'])
def login():
    username = request.json.get('username')
    if username:
        session['username'] = username
        user_id = f"user_{username}"
        if user_id not in user_environments:
            user_environments[user_id] = NetworkEnvironment(user_id)
        return jsonify({'success': True, 'username': username})
    return jsonify({'success': False})

@app.route('/simulator')
def simulator():
    if 'username' not in session:
        return redirect('/')
    return render_template('simulator.html', username=session['username'])

@app.route('/api/add_device', methods=['POST'])
def add_device():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})
        
    user_id = f"user_{session['username']}"
    env = user_environments[user_id]
    
    data = request.json
    device = env.add_device(data['type'], data['name'], data.get('x', 0), data.get('y', 0))
    
    return jsonify({
        'success': True,
        'device': {
            'id': device.id,
            'type': device.type,
            'name': device.name,
            'x': device.x,
            'y': device.y
        }
    })

@app.route('/api/execute_command', methods=['POST'])
def execute_command():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})
        
    user_id = f"user_{session['username']}"
    env = user_environments[user_id]
    
    data = request.json
    device_id = data['device_id']
    command = data['command']
    
    if device_id in env.devices:
        result = env.devices[device_id].execute_command(command)
        return jsonify({'success': True, 'output': result})
    
    return jsonify({'error': 'Device not found'})

@app.route('/api/topology')
def get_topology():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})
        
    user_id = f"user_{session['username']}"
    env = user_environments[user_id]
    
    return jsonify(env.get_topology())

@app.route('/api/configure_interface', methods=['POST'])
def configure_interface():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    env = user_environments[user_id]

    data = request.json
    device_id = data['device_id']
    interface = data['interface']
    ip_address = data['ip_address']
    subnet_mask = data['subnet_mask']

    if device_id in env.devices:
        env.devices[device_id].add_interface(interface, ip_address, subnet_mask)
        return jsonify({'success': True})

    return jsonify({'error': 'Device not found'})

@app.route('/teacher')
def teacher_dashboard():
    return render_template('teacher.html')

@app.route('/api/teacher/scores')
def get_all_scores():
    scores = teacher_dashboard_instance.get_all_scores()
    return jsonify(scores)

@app.route('/api/teacher/score/<username>')
def get_user_score(username):
    score = teacher_dashboard_instance.get_user_score(username)
    if score:
        return jsonify(score)
    return jsonify({'error': 'User not found'})

@app.route('/api/teacher/export')
def export_scores():
    csv_data = teacher_dashboard_instance.export_scores_csv()
    return Response(
        csv_data,
        mimetype='text/csv',
        headers={'Content-Disposition': 'attachment; filename=network_scores.csv'}
    )

@app.route('/api/scenarios')
def get_scenarios():
    return jsonify(practice_scenarios.get_all_scenarios())

@app.route('/api/scenario/<level>/check')
def check_scenario(level):
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    env = user_environments[user_id]

    success, message = practice_scenarios.check_scenario_completion(env, level)
    return jsonify({'success': success, 'message': message})

@app.route('/api/templates')
def get_templates():
    return jsonify({
        'templates': network_templates.get_all_templates(),
        'details': {name: network_templates.get_template(name)
                   for name in network_templates.get_all_templates()}
    })

@app.route('/api/template/<template_name>/apply', methods=['POST'])
def apply_template(template_name):
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    env = user_environments[user_id]

    success, message = network_templates.apply_template(env, template_name)
    return jsonify({'success': success, 'message': message})

@app.route('/api/validate')
def validate_network():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'})

    user_id = f"user_{session['username']}"
    env = user_environments[user_id]

    validation_result = network_validator.validate_network(env)
    return jsonify(validation_result)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
