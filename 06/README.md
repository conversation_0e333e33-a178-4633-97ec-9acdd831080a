# 网络模拟器教学工具

## 项目简介

这是一个专为小学生设计的网络模拟教学工具，通过Web界面提供直观的网络配置学习环境。

## 文件说明

### 核心文件
- `network_simulator.py` - 主服务器程序，提供Web服务和API接口
- `auto_grader.py` - 自动测试打分系统，评估学生网络配置

### 模板文件
- `templates/index.html` - 登录页面
- `templates/simulator.html` - 学生操作界面
- `templates/teacher.html` - 教师管理界面

### 静态资源
- `static/css/style.css` - 样式文件
- `static/js/simulator.js` - 前端交互逻辑

### 配置文件
- `requirements.txt` - Python依赖包列表
- `start_server.py` - 便捷启动脚本

## 功能特性

### 学生端功能
- 多用户独立环境
- 拖拽式设备添加（路由器、客户端）
- **直观的拖拽连接功能**（拖拽设备端口创建连接）
- **智能网线渲染**（SVG网线随设备移动自动更新）
- **动态端口系统**（客户端1个端口，路由器4个端口，可动态添加）
- **精确端口定位**（端口正确显示在设备边缘）
- 动态接口创建和配置
- IP地址和接口配置
- 网络命令执行（ip a, ip route, ping）
- 实时网络拓扑显示
- 网络模板快速应用（自动清空避免重复）
- 练习场景引导学习
- 实时网络配置验证

### 教师端功能
- 学生作业实时监控
- 自动评分系统
- 分数统计和分析
- CSV格式成绩导出
- 个性化改进建议

### 评分标准
- 设备数量（20分）
- 网络连通性（30分）
- IP配置正确性（25分）
- 路由复杂度（15分）
- 网络设计合理性（10分）

## 安装运行

### 方法一：使用启动脚本（推荐）
```bash
python start_server.py
```

### 方法二：手动启动
1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 启动服务
```bash
python network_simulator.py
```

3. 访问地址
- 学生端：http://localhost:8080
- 教师端：http://localhost:8080/teacher

## 使用说明

### 学生操作流程
1. 输入用户名登录
2. 添加网络设备（路由器、客户端）
3. 使用连接模式连接设备
4. 配置设备接口和IP地址
5. 执行网络命令测试连通性
6. 使用验证功能检查配置
7. 尝试练习场景提升技能

### 教师操作流程
1. 访问教师端页面
2. 查看学生实时进度
3. 查看自动评分结果
4. 导出成绩数据
5. 提供个性化指导

## 支持的网络命令

- `ip a` - 显示网络接口信息
- `ip route` - 显示路由表
- `ping <目标IP>` - 测试网络连通性

## 技术架构

- 后端：Python Flask框架
- 前端：HTML5 + CSS3 + JavaScript
- 数据存储：内存存储（会话级别）
- 网络模拟：虚拟设备对象模型
