"""
练习模板 - 中等级别

学习重点：
- 独立编写网络请求代码
- JSON嵌套数据处理
- 复杂条件判断逻辑
- 多城市天气对比
"""

import requests
import json

def get_weather_data(city):
    """
    任务1: 获取指定城市的天气数据
    
    要求：
    1. 构建正确的API URL
    2. 发送GET请求
    3. 检查状态码，成功返回response，失败返回None
    4. 添加适当的错误处理和用户提示
    """
    print(f"正在获取{city}的天气数据...")
    
    # TODO: 在这里编写你的代码
    
    pass

def parse_and_display_weather(response, city_name):
    """
    任务2: 解析并显示天气信息
    
    要求：
    1. 检查response是否为None
    2. 解析JSON数据
    3. 显示当前天气：温度、风速、描述
    4. 显示未来3天预报（如果有数据）
    5. 返回解析后的数据字典
    """
    if response is None:
        return None
    
    print(f"\n{city_name}天气信息:")
    
    # TODO: 在这里编写你的代码
    
    pass

def advanced_clothing_suggestion(data):
    """
    任务3: 高级穿衣建议系统
    
    要求：
    1. 提取温度和风速数值
    2. 实现更详细的穿衣建议逻辑：
       - 温度 < 0°C: 极寒装备
       - 0°C ≤ 温度 < 5°C: 厚重保暖
       - 5°C ≤ 温度 < 15°C: 中等保暖
       - 15°C ≤ 温度 < 25°C: 轻薄舒适
       - 温度 ≥ 25°C: 夏季清凉
    3. 考虑风速影响：风速 > 10km/h 时建议加强防风
    4. 根据天气描述给出特殊建议（如雨天带伞）
    """
    if data is None:
        return
    
    print("\n智能穿衣建议:")
    
    # TODO: 在这里编写你的代码
    
    pass

def compare_cities_weather():
    """
    任务4: 多城市天气对比
    
    要求：
    1. 获取至少3个城市的天气数据（beijing, shanghai, guangzhou）
    2. 对比显示各城市的温度和风速
    3. 找出温度最高和最低的城市
    4. 给出旅行建议
    """
    cities = ["beijing", "shanghai", "guangzhou"]
    weather_data = {}
    
    print("\n多城市天气对比:")
    
    # TODO: 在这里编写你的代码
    
    pass

def forecast_analysis(data):
    """
    挑战任务: 预报趋势分析 (可选)
    
    要求：
    1. 分析未来几天的温度变化趋势
    2. 判断是升温还是降温
    3. 给出相应的生活建议
    """
    if data is None or 'forecast' not in data:
        return
    
    print("\n天气趋势分析:")
    
    # TODO: 在这里编写你的代码 (挑战任务，可选)
    
    pass

def main():
    """
    主程序
    """
    print("=" * 50)
    print("天气查询练习 - 中等级别")
    print("=" * 50)
    
    # 单城市天气查询
    city = "beijing"
    response = get_weather_data(city)
    data = parse_and_display_weather(response, "北京")
    advanced_clothing_suggestion(data)
    forecast_analysis(data)
    
    # 多城市对比
    compare_cities_weather()
    
    print("\n练习完成！")

if __name__ == "__main__":
    main()
