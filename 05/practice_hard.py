"""
练习模板 - 困难级别

项目要求：
创建一个完整的天气查询和分析系统，实现以下功能：

核心功能：
1. 多城市天气数据获取和解析
2. JSON嵌套数据的深度处理
3. 智能穿衣建议系统
4. 天气数据对比和分析

扩展功能 (挑战)：
5. 天气数据持久化存储
6. 历史天气数据管理
7. 天气趋势预测分析
8. 用户交互界面和菜单系统
9. 天气预警功能
10. 数据可视化输出

技术要求：
- 使用面向对象编程 (创建类)
- 实现完整的错误处理
- 代码结构清晰，模块化设计
- 用户界面友好
- 数据处理高效

评分标准：
- 基础功能实现 (50分)
- 数据处理准确性 (20分)
- 错误处理完善 (15分)  
- 代码结构清晰 (10分)
- 挑战功能实现 (5分)
"""

import requests
import json
from datetime import datetime
import os
import time

class WeatherAnalyzer:
    """
    天气分析器类
    
    你需要实现以下方法：
    - __init__: 初始化方法
    - get_weather: 获取天气数据
    - parse_weather_data: 解析天气数据
    - clothing_advisor: 穿衣建议系统
    - compare_cities: 城市天气对比
    - save_data: 数据保存 (挑战)
    - load_history: 加载历史数据 (挑战)
    - trend_analysis: 趋势分析 (挑战)
    - weather_alert: 天气预警 (挑战)
    - interactive_menu: 交互菜单
    - run: 主运行方法
    """
    
    def __init__(self):
        """
        初始化天气分析器
        
        TODO: 设置必要的属性
        - API基础URL
        - 支持的城市列表
        - 数据存储结构
        - 历史数据文件路径
        """
        pass
    
    def get_weather(self, city):
        """
        获取指定城市的天气数据
        
        TODO: 实现网络请求逻辑
        参数: city - 城市名称（拼音）
        返回: 解析后的天气数据字典或None
        """
        pass
    
    def parse_weather_data(self, data):
        """
        深度解析天气数据
        
        TODO: 实现数据解析和格式化
        参数: data - 原始天气数据
        返回: 格式化的天气信息字典
        """
        pass
    
    def clothing_advisor(self, weather_info):
        """
        智能穿衣建议系统
        
        TODO: 实现复杂的穿衣建议逻辑
        考虑因素：
        - 温度范围
        - 风速影响
        - 天气状况
        - 季节因素
        参数: weather_info - 天气信息字典
        返回: 穿衣建议字符串
        """
        pass
    
    def compare_cities(self, cities):
        """
        多城市天气对比分析
        
        TODO: 实现城市间天气对比
        参数: cities - 城市列表
        返回: 对比分析结果
        """
        pass
    
    def save_data(self, city, weather_data):
        """
        保存天气数据到文件 (挑战功能)
        
        TODO: 实现数据持久化
        参数: 
        - city: 城市名称
        - weather_data: 天气数据
        """
        pass
    
    def load_history(self, city):
        """
        加载历史天气数据 (挑战功能)
        
        TODO: 从文件加载历史数据
        参数: city - 城市名称
        返回: 历史数据列表
        """
        pass
    
    def trend_analysis(self, city):
        """
        天气趋势分析 (挑战功能)
        
        TODO: 分析天气变化趋势
        参数: city - 城市名称
        返回: 趋势分析结果
        """
        pass
    
    def weather_alert(self, weather_info):
        """
        天气预警系统 (挑战功能)
        
        TODO: 根据天气条件发出预警
        参数: weather_info - 天气信息
        返回: 预警信息列表
        """
        pass
    
    def display_weather_report(self, city, weather_info):
        """
        显示完整的天气报告
        
        TODO: 格式化显示天气信息
        参数:
        - city: 城市名称
        - weather_info: 天气信息字典
        """
        pass
    
    def interactive_menu(self):
        """
        交互式菜单系统
        
        TODO: 实现用户交互界面
        提供选项：
        1. 查询单个城市天气
        2. 多城市对比
        3. 历史数据查看
        4. 趋势分析
        5. 退出程序
        """
        pass
    
    def run(self):
        """
        主运行方法
        
        TODO: 实现主程序逻辑
        - 显示欢迎信息
        - 启动交互菜单
        - 处理用户选择
        """
        pass

def main():
    """
    程序入口点
    """
    # TODO: 创建WeatherAnalyzer实例并运行
    pass

if __name__ == "__main__":
    main()
