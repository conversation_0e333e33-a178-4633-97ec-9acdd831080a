"""
自动测试评分系统
用于评估 practice_hard.py 的完成质量

评分标准：
- 基础功能实现 (50分)
- 数据处理准确性 (20分)
- 错误处理完善 (15分)  
- 代码结构清晰 (10分)
- 挑战功能实现 (5分)
"""

import importlib.util
import sys
import io
import contextlib
import traceback
import inspect
import requests
import json
from datetime import datetime
import os

class WeatherGrader:
    def __init__(self, student_file="practice_hard.py"):
        self.student_file = student_file
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
        self.student_module = None
        
    def load_student_code(self):
        """加载学生的代码文件"""
        try:
            spec = importlib.util.spec_from_file_location("student_code", self.student_file)
            self.student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.student_module)
            return True
        except Exception as e:
            self.add_result("代码加载", 0, 5, f"无法加载代码文件: {e}")
            return False
    
    def add_result(self, test_name, score, max_score, comment=""):
        """添加测试结果"""
        self.test_results.append({
            'name': test_name,
            'score': score,
            'max_score': max_score,
            'comment': comment
        })
        self.total_score += score
    
    def test_class_structure(self):
        """测试类结构 (10分)"""
        try:
            if hasattr(self.student_module, 'WeatherAnalyzer'):
                weather_class = self.student_module.WeatherAnalyzer
                
                # 检查必要方法
                required_methods = [
                    '__init__', 'get_weather', 'parse_weather_data',
                    'clothing_advisor', 'compare_cities', 'run'
                ]
                
                missing_methods = []
                for method in required_methods:
                    if not hasattr(weather_class, method):
                        missing_methods.append(method)
                
                if not missing_methods:
                    self.add_result("类结构", 10, 10, "所有必要方法都已实现")
                else:
                    score = max(0, 10 - len(missing_methods) * 2)
                    self.add_result("类结构", score, 10, f"缺少方法: {', '.join(missing_methods)}")
            else:
                self.add_result("类结构", 0, 10, "未找到WeatherAnalyzer类")
        except Exception as e:
            self.add_result("类结构", 0, 10, f"类结构检查失败: {e}")
    
    def test_initialization(self):
        """测试初始化方法 (5分)"""
        try:
            if hasattr(self.student_module, 'WeatherAnalyzer'):
                analyzer = self.student_module.WeatherAnalyzer()
                
                # 检查基本属性
                score = 0
                comments = []
                
                if hasattr(analyzer, 'base_url') or hasattr(analyzer, 'url'):
                    score += 2
                    comments.append("URL配置正确")
                
                if hasattr(analyzer, 'supported_cities') or hasattr(analyzer, 'cities'):
                    score += 2
                    comments.append("城市列表配置正确")
                
                if hasattr(analyzer, 'weather_cache') or hasattr(analyzer, 'cache'):
                    score += 1
                    comments.append("缓存机制配置")
                
                self.add_result("初始化", score, 5, "; ".join(comments) if comments else "初始化不完整")
            else:
                self.add_result("初始化", 0, 5, "未找到WeatherAnalyzer类")
        except Exception as e:
            self.add_result("初始化", 0, 5, f"初始化测试失败: {e}")
    
    def test_get_weather_function(self):
        """测试获取天气功能 (15分)"""
        try:
            if hasattr(self.student_module, 'WeatherAnalyzer'):
                analyzer = self.student_module.WeatherAnalyzer()
                
                if hasattr(analyzer, 'get_weather'):
                    # 测试正常城市
                    result = analyzer.get_weather('beijing')
                    
                    if result is not None:
                        if isinstance(result, dict):
                            score = 15
                            comment = "天气数据获取功能正常"
                        else:
                            score = 10
                            comment = "返回数据格式需要改进"
                    else:
                        score = 5
                        comment = "无法获取天气数据，可能是网络问题或实现不完整"
                    
                    self.add_result("天气获取", score, 15, comment)
                else:
                    self.add_result("天气获取", 0, 15, "未实现get_weather方法")
            else:
                self.add_result("天气获取", 0, 15, "未找到WeatherAnalyzer类")
        except Exception as e:
            self.add_result("天气获取", 0, 15, f"天气获取测试失败: {e}")
    
    def test_data_parsing(self):
        """测试数据解析功能 (20分)"""
        try:
            if hasattr(self.student_module, 'WeatherAnalyzer'):
                analyzer = self.student_module.WeatherAnalyzer()
                
                if hasattr(analyzer, 'parse_weather_data'):
                    # 创建测试数据
                    test_data = {
                        'temperature': '25 °C',
                        'wind': '10 km/h',
                        'description': 'Clear',
                        'forecast': [
                            {'day': '1', 'temperature': '28 °C', 'wind': '8 km/h'}
                        ]
                    }
                    
                    result = analyzer.parse_weather_data(test_data)
                    
                    if result is not None:
                        score = 0
                        comments = []
                        
                        # 检查基本数据提取
                        if isinstance(result, dict):
                            score += 5
                            comments.append("返回字典格式")
                        
                        # 检查是否正确处理温度
                        if 'temperature' in str(result) or 'temp' in str(result):
                            score += 5
                            comments.append("温度数据处理")
                        
                        # 检查是否处理风速
                        if 'wind' in str(result):
                            score += 5
                            comments.append("风速数据处理")
                        
                        # 检查是否处理预报数据
                        if 'forecast' in str(result):
                            score += 5
                            comments.append("预报数据处理")
                        
                        self.add_result("数据解析", score, 20, "; ".join(comments))
                    else:
                        self.add_result("数据解析", 0, 20, "数据解析返回None")
                else:
                    self.add_result("数据解析", 0, 20, "未实现parse_weather_data方法")
            else:
                self.add_result("数据解析", 0, 20, "未找到WeatherAnalyzer类")
        except Exception as e:
            self.add_result("数据解析", 5, 20, f"数据解析测试失败，但有基本实现: {e}")
    
    def test_clothing_advisor(self):
        """测试穿衣建议功能 (10分)"""
        try:
            if hasattr(self.student_module, 'WeatherAnalyzer'):
                analyzer = self.student_module.WeatherAnalyzer()
                
                if hasattr(analyzer, 'clothing_advisor'):
                    # 创建测试天气信息
                    test_weather = {
                        'current': {
                            'temperature': '25 °C',
                            'wind': '10 km/h',
                            'description': 'Clear',
                            'temp_value': 25,
                            'wind_value': 10
                        }
                    }
                    
                    result = analyzer.clothing_advisor(test_weather)
                    
                    if result and isinstance(result, str) and len(result) > 10:
                        self.add_result("穿衣建议", 10, 10, "穿衣建议功能正常")
                    elif result:
                        self.add_result("穿衣建议", 6, 10, "穿衣建议功能基本实现")
                    else:
                        self.add_result("穿衣建议", 0, 10, "穿衣建议功能未正常工作")
                else:
                    self.add_result("穿衣建议", 0, 10, "未实现clothing_advisor方法")
            else:
                self.add_result("穿衣建议", 0, 10, "未找到WeatherAnalyzer类")
        except Exception as e:
            self.add_result("穿衣建议", 3, 10, f"穿衣建议测试失败，但有基本实现: {e}")
    
    def test_error_handling(self):
        """测试错误处理 (15分)"""
        try:
            if hasattr(self.student_module, 'WeatherAnalyzer'):
                analyzer = self.student_module.WeatherAnalyzer()
                
                score = 0
                comments = []
                
                # 测试无效城市处理
                if hasattr(analyzer, 'get_weather'):
                    try:
                        result = analyzer.get_weather('invalid_city_12345')
                        if result is None:
                            score += 5
                            comments.append("无效城市处理正确")
                    except Exception:
                        score += 3
                        comments.append("无效城市有异常处理")
                
                # 测试空数据处理
                if hasattr(analyzer, 'parse_weather_data'):
                    try:
                        result = analyzer.parse_weather_data(None)
                        if result is None:
                            score += 5
                            comments.append("空数据处理正确")
                    except Exception:
                        score += 2
                        comments.append("空数据有异常处理")
                
                # 测试穿衣建议错误处理
                if hasattr(analyzer, 'clothing_advisor'):
                    try:
                        result = analyzer.clothing_advisor(None)
                        if result:
                            score += 5
                            comments.append("穿衣建议错误处理正确")
                    except Exception:
                        score += 2
                        comments.append("穿衣建议有异常处理")
                
                self.add_result("错误处理", score, 15, "; ".join(comments) if comments else "错误处理不完善")
            else:
                self.add_result("错误处理", 0, 15, "未找到WeatherAnalyzer类")
        except Exception as e:
            self.add_result("错误处理", 0, 15, f"错误处理测试失败: {e}")
    
    def test_bonus_features(self):
        """测试挑战功能 (5分)"""
        try:
            if hasattr(self.student_module, 'WeatherAnalyzer'):
                analyzer = self.student_module.WeatherAnalyzer()
                
                score = 0
                comments = []
                
                # 检查数据保存功能
                if hasattr(analyzer, 'save_data'):
                    score += 1
                    comments.append("数据保存功能")
                
                # 检查历史数据功能
                if hasattr(analyzer, 'load_history'):
                    score += 1
                    comments.append("历史数据功能")
                
                # 检查城市对比功能
                if hasattr(analyzer, 'compare_cities'):
                    score += 1
                    comments.append("城市对比功能")
                
                # 检查交互菜单
                if hasattr(analyzer, 'interactive_menu'):
                    score += 1
                    comments.append("交互菜单")
                
                # 检查预警功能
                if hasattr(analyzer, 'weather_alert'):
                    score += 1
                    comments.append("天气预警功能")
                
                self.add_result("挑战功能", score, 5, "; ".join(comments) if comments else "未实现挑战功能")
            else:
                self.add_result("挑战功能", 0, 5, "未找到WeatherAnalyzer类")
        except Exception as e:
            self.add_result("挑战功能", 0, 5, f"挑战功能测试失败: {e}")
    
    def test_main_function(self):
        """测试主函数 (10分)"""
        try:
            if hasattr(self.student_module, 'main'):
                # 检查main函数是否存在并可调用
                main_func = self.student_module.main
                if callable(main_func):
                    self.add_result("主函数", 10, 10, "主函数实现正确")
                else:
                    self.add_result("主函数", 0, 10, "main不是可调用函数")
            else:
                self.add_result("主函数", 0, 10, "未找到main函数")
        except Exception as e:
            self.add_result("主函数", 5, 10, f"主函数测试失败，但有基本实现: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始自动评分...")
        print("=" * 60)
        
        if not self.load_student_code():
            return
        
        # 运行各项测试
        self.test_class_structure()
        self.test_initialization()
        self.test_get_weather_function()
        self.test_data_parsing()
        self.test_clothing_advisor()
        self.test_error_handling()
        self.test_bonus_features()
        self.test_main_function()
        
        # 显示结果
        self.display_results()
    
    def display_results(self):
        """显示评分结果"""
        print("\n" + "=" * 60)
        print("评分结果")
        print("=" * 60)
        
        for result in self.test_results:
            status = "✓" if result['score'] == result['max_score'] else "✗" if result['score'] == 0 else "△"
            print(f"{status} {result['name']:<15} {result['score']:>3}/{result['max_score']:<3} {result['comment']}")
        
        print("-" * 60)
        print(f"总分: {self.total_score}/{self.max_score}")
        
        # 等级评定
        percentage = (self.total_score / self.max_score) * 100
        if percentage >= 90:
            grade = "优秀 (A)"
        elif percentage >= 80:
            grade = "良好 (B)"
        elif percentage >= 70:
            grade = "中等 (C)"
        elif percentage >= 60:
            grade = "及格 (D)"
        else:
            grade = "不及格 (F)"
        
        print(f"得分率: {percentage:.1f}%")
        print(f"等级: {grade}")
        
        # 改进建议
        self.provide_suggestions()
    
    def provide_suggestions(self):
        """提供改进建议"""
        print("\n" + "=" * 60)
        print("改进建议")
        print("=" * 60)
        
        low_score_tests = [r for r in self.test_results if r['score'] < r['max_score'] * 0.7]
        
        if not low_score_tests:
            print("代码质量很好！继续保持。")
        else:
            for test in low_score_tests:
                if test['name'] == '类结构':
                    print("• 确保实现所有必要的方法")
                elif test['name'] == '天气获取':
                    print("• 检查网络请求实现和错误处理")
                elif test['name'] == '数据解析':
                    print("• 完善JSON数据解析逻辑")
                elif test['name'] == '错误处理':
                    print("• 添加更完善的异常处理机制")
                elif test['name'] == '挑战功能':
                    print("• 尝试实现更多高级功能")

def main():
    """主程序"""
    grader = WeatherGrader()
    grader.run_all_tests()

if __name__ == "__main__":
    main()
