# L5 - 天气API与JSON嵌套

## 教学目标

本项目通过天气API学习Python网络编程进阶概念：

- **JSON嵌套结构** - 理解字典中包含字典和列表
- **数据访问** - 掌握多层数据的访问方法
- **条件判断** - 根据数据进行逻辑判断
- **实际应用** - 结合生活场景的编程实践

## API说明

使用免费天气API：`http://goweather.xyz/weather/{城市拼音}`

返回JSON格式：
```json
{
  "temperature": "29 °C",
  "wind": "5 km/h", 
  "description": "Clear",
  "forecast": [
    {
      "day": "1",
      "temperature": "34 °C",
      "wind": "6 km/h"
    }
  ]
}
```

## 文件说明

### 1. 分层次练习模板 (三个难度级别)

#### `practice_easy.py` - 简单级别
- **适合对象**: 初学者，了解基础语法
- **练习方式**: 填空练习，重点学习JSON嵌套访问
- **学习重点**: 字典嵌套、列表访问、数据提取
- **完成时间**: 15-20分钟

#### `practice_medium.py` - 中等级别  
- **适合对象**: 有Python基础，了解网络请求
- **练习方式**: 半引导式，给出函数框架
- **学习重点**: 独立编写逻辑，条件判断，穿衣建议
- **完成时间**: 25-35分钟

#### `practice_hard.py` - 困难级别
- **适合对象**: 有编程经验，想要挑战
- **练习方式**: 只给出类框架，完全自主实现
- **学习重点**: 面向对象编程，完整应用开发
- **完成时间**: 45-60分钟

### 2. `practice_answers.py` - 练习答案
- 完整的答案版本
- 供老师参考和学生对照

### 3. `auto_grader.py` - 自动评分
- **适用对象**: 困难级别练习 (`practice_hard.py`)
- **评分内容**: 7个维度，总分100分
- **功能特点**:
  - 自动测试代码功能
  - 详细的评分报告
  - 等级评定和改进建议
- **使用方法**: `python auto_grader.py`
