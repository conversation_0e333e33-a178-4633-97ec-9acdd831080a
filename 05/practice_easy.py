"""
练习模板 - 简单级别

学习重点：
- JSON嵌套结构访问
- 字典中的列表操作
- 条件判断与穿衣建议
- 数据提取和处理
"""

import requests
import json

def get_weather_data():
    """
    步骤1: 获取天气数据
    请根据提示填入正确的代码
    """
    print("开始获取天气数据...")
    
    # 提示：北京的拼音是beijing
    city = "beijing"
    url = f"http://goweather.xyz/weather/{city}"
    
    # TODO: 使用requests库发送GET请求
    response = requests._____(url)
    
    # TODO: 检查状态码是否为200
    if response._______ == ___:
        print("请求成功!")
        return response
    else:
        print("请求失败!")
        return None

def show_raw_data(response):
    """
    步骤2: 显示原始数据
    """
    if response is None:
        return
    
    print("\n服务器返回的原始数据:")
    # TODO: 打印response的text属性
    print(response._______)

def parse_weather_data(response):
    """
    步骤3: 解析天气数据
    """
    if response is None:
        return None
    
    # TODO: 将JSON字符串转换为Python字典
    data = json._____(response._______)
    
    print("\n当前天气信息:")
    # TODO: 获取温度信息
    print(f"温度: {data['_______']}")
    
    # TODO: 获取风速信息  
    print(f"风速: {data['_____']}")
    
    # TODO: 获取天气描述
    print(f"天气: {data['_______']}")
    
    return data

def show_forecast_data(data):
    """
    步骤4: 显示预报数据 (JSON嵌套访问)
    """
    if data is None:
        return
    
    print("\n未来天气预报:")
    
    # TODO: 获取forecast列表
    forecast_list = data['_______']
    
    # TODO: 获取第一天的预报数据
    first_day = forecast_list[_]
    
    print(f"第1天:")
    # TODO: 获取第一天的温度
    print(f"  温度: {first_day['_______']}")
    # TODO: 获取第一天的风速
    print(f"  风速: {first_day['_____']}")
    
    # TODO: 获取第二天的预报数据
    second_day = forecast_list[_]
    
    print(f"第2天:")
    # TODO: 获取第二天的温度和风速
    print(f"  温度: {second_day['_______']}")
    print(f"  风速: {second_day['_____']}")

def clothing_suggestion(data):
    """
    步骤5: 根据天气给出穿衣建议
    """
    if data is None:
        return
    
    print("\n穿衣建议:")
    
    # TODO: 获取当前温度字符串，例如"29 °C"
    temp_str = data['_______']
    
    # TODO: 提取温度数字（去掉" °C"部分）
    temp_num = int(temp_str.replace(' °C', ''))
    
    # TODO: 获取风速字符串，例如"5 km/h"
    wind_str = data['_____']
    
    # TODO: 提取风速数字（去掉" km/h"部分）
    wind_num = int(wind_str.replace(' km/h', ''))
    
    print(f"温度: {temp_num}°C, 风速: {wind_num}km/h")
    
    # TODO: 根据温度和风速给出建议
    if temp_num < 5 and wind_num > 5:
        print("建议穿着：厚羽绒服、毛衣、保暖内衣、帽子、围巾、手套")
    elif temp_num > 15 and temp_num < 25:
        print("建议穿着：薄外套或针织衫、T恤、长裤")
    elif temp_num >= 25:
        print("建议穿着：T恤、短裤、防晒帽")
    else:
        print("建议穿着：长袖衬衫、薄外套、长裤")

def main():
    """
    主程序：按顺序执行所有步骤
    """
    print("=" * 50)
    print("天气查询练习 - 简单级别")
    print("=" * 50)
    
    # 执行所有步骤
    response = get_weather_data()
    show_raw_data(response)
    data = parse_weather_data(response)
    show_forecast_data(data)
    clothing_suggestion(data)
    
    print("\n练习完成！")

if __name__ == "__main__":
    main()

"""
答案提示：
1. get
2. status_code
3. 200
4. text (出现2次)
5. loads
6. temperature (出现4次)
7. wind (出现4次)  
8. description
9. forecast
10. 0 (第一个元素)
11. 1 (第二个元素)
"""
