"""
练习模板 - 中等级别

学习重点：
- 独立编写汇率API请求代码
- 理解JSON数据结构和解析
- 掌握matplotlib基础绘图
- 数据处理和可视化
"""

import requests
import json
import matplotlib.pyplot as plt

def get_current_rate():
    """
    任务1: 获取当前汇率数据
    
    要求：
    1. 定义API的URL: "https://api.frankfurter.app/latest?from=JPY&to=CNY"
    2. 发送GET请求
    3. 检查状态码，如果是200则返回response，否则返回None
    4. 添加适当的打印信息告知用户请求状态
    """
    print("开始获取当前汇率...")
    
    # TODO: 在这里编写你的代码
    
    pass  # 删除这行，写入你的代码

def parse_rate_data(response):
    """
    任务2: 解析汇率数据
    
    要求：
    1. 检查response是否为None
    2. 打印原始的response.text
    3. 将JSON字符串转换为Python字典
    4. 提取并返回CNY汇率值
    5. 如果解析失败，打印错误信息并返回None
    """
    print("\n解析汇率数据...")
    
    # TODO: 在这里编写你的代码
    
    pass  # 删除这行，写入你的代码

def get_historical_data():
    """
    任务3: 获取历史汇率数据
    
    要求：
    1. 使用API获取最近一周的历史数据
    2. API地址: "https://api.frankfurter.app/2025-06-19..2025-06-25?from=JPY&to=CNY"
    3. 解析返回的JSON数据
    4. 提取日期和汇率数据，返回两个列表：dates, rates
    5. 添加错误处理
    """
    print("\n获取历史汇率数据...")
    
    # TODO: 在这里编写你的代码
    
    pass  # 删除这行，写入你的代码

def create_rate_chart(dates, rates):
    """
    任务4: 创建汇率图表
    
    要求：
    1. 检查dates和rates是否为None或空列表
    2. 使用plt.plot()创建线图
    3. 设置图表标题: "JPY to CNY Exchange Rate Trend"
    4. 设置x轴标签: "Date"
    5. 设置y轴标签: "Exchange Rate (CNY)"
    6. 显示网格
    7. 旋转x轴标签45度以便显示
    8. 显示图表
    """
    print("\n创建汇率趋势图...")
    
    # TODO: 在这里编写你的代码
    
    pass  # 删除这行，写入你的代码

def analyze_trend(rates):
    """
    挑战任务: 分析汇率趋势 (可选)
    
    要求：
    1. 计算平均汇率
    2. 找出最高和最低汇率
    3. 计算汇率变化幅度
    4. 显示分析结果
    """
    if not rates:
        return
    
    print("\n汇率趋势分析:")
    
    # TODO: 在这里编写你的代码 (挑战任务，可选)
    
    pass  # 删除这行，写入你的代码

def main():
    """
    主程序
    """
    print("=" * 50)
    print("汇率API和图表练习 - 中等级别")
    print("=" * 50)
    
    # 获取当前汇率
    response = get_current_rate()
    current_rate = parse_rate_data(response)
    
    if current_rate:
        print(f"\n当前汇率: 1 JPY = {current_rate} CNY")
    
    # 获取历史数据并绘图
    dates, rates = get_historical_data()
    if dates and rates:
        create_rate_chart(dates, rates)
        analyze_trend(rates)  # 挑战任务
    
    print("\n练习完成！")

if __name__ == "__main__":
    main()
