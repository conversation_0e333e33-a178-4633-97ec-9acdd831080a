# L2 - 汇率API与数据可视化

## 教学目标

本项目通过汇率API学习Python网络编程和数据可视化基础：

- **requests.get()** - 发送HTTP请求获取汇率数据
- **JSON数据解析** - 从API响应中提取目标货币汇率
- **matplotlib基础** - plt.plot(), plt.title(), plt.xlabel(), plt.ylabel(), plt.grid(), plt.show()
- **数据处理** - 历史数据获取和趋势分析

## 文件说明

### 1. 分层次练习模板 (三个难度级别)

#### `practice_easy.py` - 简单级别
- **适合对象**: 完全初学者，第一次接触API和绘图
- **练习方式**: 填空练习，重点学习JSON解析和matplotlib基础函数
- **学习重点**: 从汇率API的JSON响应中找到目标货币汇率，掌握基础绘图
- **完成时间**: 15-20分钟

#### `practice_medium.py` - 中等级别
- **适合对象**: 有Python基础，了解基本语法
- **练习方式**: 半引导式，给出函数框架和要求
- **学习重点**: 独立实现API调用、数据解析和图表创建
- **完成时间**: 25-35分钟

#### `practice_hard.py` - 困难级别
- **适合对象**: 有编程经验，想要挑战自己
- **练习方式**: 只给出类框架，需要完全自主实现
- **学习重点**: 面向对象编程，完整的汇率追踪系统
- **完成时间**: 45-60分钟

### 2. `practice_answers.py` - 练习答案
- 包含所有难度级别的完整答案
- 供教师参考和学生对照

### 3. `auto_grader.py` - 自动评分
- **适用对象**: 困难级别练习 (`practice_hard.py`)
- **评分内容**: 7个维度，总分100分
- **功能特点**:
  - 自动测试API调用功能
  - 检查数据解析和图表创建
  - 详细的评分报告和改进建议
- **使用方法**: `python auto_grader.py`

## API说明

### 当前汇率API
```
https://api.frankfurter.app/latest?from=JPY&to=CNY
```

### 历史汇率API
```
https://api.frankfurter.app/2025-06-19..2025-06-25?from=JPY&to=CNY
```

## 重点学习内容

### JSON数据解析
学习如何从API返回的JSON中提取目标货币汇率：
```python
data = json.loads(response.text)
cny_rate = data['rates']['CNY']
```

### Matplotlib基础绘图
掌握基础的图表创建函数：
```python
plt.plot(dates, rates)      # 创建线图
plt.title("标题")           # 设置标题
plt.xlabel("X轴标签")       # 设置X轴标签
plt.ylabel("Y轴标签")       # 设置Y轴标签
plt.grid()                  # 显示网格
plt.show()                  # 显示图表
```

## 使用建议

1. **循序渐进**: 从简单级别开始，逐步提高难度
2. **重点理解**: 专注于JSON数据结构和matplotlib基础函数
3. **实践为主**: 多动手练习，理解API调用和数据可视化流程
4. **错误处理**: 在中等和困难级别中注意网络请求的错误处理

## 依赖库

```bash
pip install requests matplotlib
```
