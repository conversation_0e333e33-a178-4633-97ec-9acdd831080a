"""
练习答案 - 完整参考版本
包含所有难度级别的完整实现
"""

import requests
import json
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import csv

# ============ 简单级别答案 ============

def get_exchange_rate_easy():
    """简单级别 - 获取汇率数据"""
    print("开始获取汇率数据...")
    
    url = "https://api.frankfurter.app/latest?from=JPY&to=CNY"
    response = requests.get(url)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("请求成功!")
        return response
    else:
        print("请求失败!")
        return None

def parse_exchange_data_easy(response):
    """简单级别 - 解析汇率数据"""
    if response is None:
        return None
    
    data = json.loads(response.text)
    
    print("\n解析后的汇率数据:")
    print(f"基础货币: {data['base']}")
    print(f"日期: {data['date']}")
    
    cny_rate = data['rates']['CNY']
    print(f"1 JPY = {cny_rate} CNY")
    
    return data

def create_simple_chart_easy():
    """简单级别 - 创建图表"""
    print("\n创建汇率图表...")
    
    dates = ['2025-06-20', '2025-06-21', '2025-06-22', '2025-06-23', '2025-06-24', '2025-06-25']
    rates = [0.045, 0.046, 0.044, 0.047, 0.045, 0.046]
    
    plt.plot(dates, rates)
    plt.title("JPY to CNY Exchange Rate")
    plt.xlabel("Date")
    plt.ylabel("Exchange Rate")
    plt.grid()
    plt.xticks(rotation=45)
    plt.show()

# ============ 中等级别答案 ============

def get_current_rate_medium():
    """中等级别 - 获取当前汇率"""
    print("开始获取当前汇率...")
    
    url = "https://api.frankfurter.app/latest?from=JPY&to=CNY"
    
    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("请求成功!")
            return response
        else:
            print("请求失败!")
            return None
    except Exception as e:
        print(f"请求出错: {e}")
        return None

def parse_rate_data_medium(response):
    """中等级别 - 解析汇率数据"""
    print("\n解析汇率数据...")
    
    if response is None:
        return None
    
    try:
        print("原始数据:", response.text)
        data = json.loads(response.text)
        
        cny_rate = data['rates']['CNY']
        print(f"解析成功: 1 JPY = {cny_rate} CNY")
        return cny_rate
    except Exception as e:
        print(f"解析失败: {e}")
        return None

def get_historical_data_medium():
    """中等级别 - 获取历史数据"""
    print("\n获取历史汇率数据...")
    
    url = "https://api.frankfurter.app/2025-06-19..2025-06-25?from=JPY&to=CNY"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = json.loads(response.text)
            
            dates = []
            rates = []
            
            for date, rate_data in data['rates'].items():
                dates.append(date)
                rates.append(rate_data['CNY'])
            
            # 按日期排序
            date_rate_pairs = list(zip(dates, rates))
            date_rate_pairs.sort()
            dates, rates = zip(*date_rate_pairs)
            
            return list(dates), list(rates)
        else:
            print("获取历史数据失败")
            return None, None
    except Exception as e:
        print(f"获取历史数据出错: {e}")
        return None, None

def create_rate_chart_medium(dates, rates):
    """中等级别 - 创建图表"""
    print("\n创建汇率趋势图...")
    
    if not dates or not rates:
        print("数据为空，无法创建图表")
        return
    
    plt.figure(figsize=(10, 6))
    plt.plot(dates, rates, marker='o')
    plt.title("JPY to CNY Exchange Rate Trend")
    plt.xlabel("Date")
    plt.ylabel("Exchange Rate (CNY)")
    plt.grid(True)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

# ============ 困难级别答案 ============

class CurrencyTracker:
    """汇率追踪器完整实现"""
    
    def __init__(self, base_currency="JPY", target_currency="CNY"):
        self.base_currency = base_currency
        self.target_currency = target_currency
        self.base_url = "https://api.frankfurter.app"
        self.data_history = []
        
    def get_current_rate(self):
        """获取当前汇率"""
        url = f"{self.base_url}/latest?from={self.base_currency}&to={self.target_currency}"
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                data = self.parse_data(response)
                if data:
                    return data['rates'][self.target_currency]
            return None
        except Exception as e:
            print(f"获取当前汇率失败: {e}")
            return None
    
    def get_historical_rates(self, days=7):
        """获取历史汇率数据"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        url = f"{self.base_url}/{start_str}..{end_str}?from={self.base_currency}&to={self.target_currency}"
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                data = self.parse_data(response)
                if data and 'rates' in data:
                    dates = []
                    rates = []
                    
                    for date, rate_data in data['rates'].items():
                        dates.append(date)
                        rates.append(rate_data[self.target_currency])
                    
                    # 排序
                    date_rate_pairs = list(zip(dates, rates))
                    date_rate_pairs.sort()
                    dates, rates = zip(*date_rate_pairs)
                    
                    return list(dates), list(rates)
            return None, None
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return None, None
    
    def parse_data(self, response):
        """解析数据"""
        try:
            return json.loads(response.text)
        except Exception as e:
            print(f"数据解析失败: {e}")
            return None
    
    def create_chart(self, dates, rates, title="Exchange Rate Trend"):
        """创建图表"""
        if not dates or not rates:
            print("数据为空，无法创建图表")
            return
        
        plt.figure(figsize=(12, 6))
        plt.plot(dates, rates, marker='o', linewidth=2, markersize=6)
        plt.title(f"{self.base_currency} to {self.target_currency} {title}")
        plt.xlabel("Date")
        plt.ylabel(f"Exchange Rate ({self.target_currency})")
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()
    
    def analyze_rates(self, rates):
        """分析汇率"""
        if not rates:
            return
        
        avg_rate = sum(rates) / len(rates)
        max_rate = max(rates)
        min_rate = min(rates)
        volatility = max_rate - min_rate
        
        print(f"\n汇率分析报告:")
        print(f"平均汇率: {avg_rate:.6f}")
        print(f"最高汇率: {max_rate:.6f}")
        print(f"最低汇率: {min_rate:.6f}")
        print(f"波动幅度: {volatility:.6f}")
        print(f"波动率: {(volatility/avg_rate)*100:.2f}%")
    
    def save_data(self, dates, rates, filename="exchange_rates.csv"):
        """保存数据到文件"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(['Date', 'Rate'])
                for date, rate in zip(dates, rates):
                    writer.writerow([date, rate])
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def run(self):
        """主运行方法"""
        print("=" * 50)
        print(f"{self.base_currency} to {self.target_currency} 汇率追踪器")
        print("=" * 50)
        
        # 获取当前汇率
        current_rate = self.get_current_rate()
        if current_rate:
            print(f"当前汇率: 1 {self.base_currency} = {current_rate} {self.target_currency}")
        
        # 获取历史数据
        dates, rates = self.get_historical_rates()
        if dates and rates:
            print(f"\n获取到 {len(dates)} 天的历史数据")
            self.create_chart(dates, rates)
            self.analyze_rates(rates)
            self.save_data(dates, rates)
        
        print("\n程序运行完成!")

def main():
    """主程序"""
    tracker = CurrencyTracker()
    tracker.run()

if __name__ == "__main__":
    main()
