"""
简单级别练习答案
填空题的完整答案版本
"""

import requests
import json
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

def get_exchange_rate():
    """
    步骤1: 获取汇率数据
    """
    print("开始获取汇率数据...")
    
    url = "https://api.frankfurter.app/latest?from=JPY&to=CNY"
    
    # 答案: get
    response = requests.get(url)
    
    # 答案: status_code
    print(f"状态码: {response.status_code}")
    
    # 答案: status_code, 200
    if response.status_code == 200:
        print("请求成功!")
        return response
    else:
        print("请求失败!")
        return None

def show_raw_data(response):
    """
    步骤2: 显示服务器返回的原始数据
    """
    if response is None:
        return
    
    print("\n服务器返回的原始数据:")
    # 答案: text
    print(response.text)

def parse_exchange_data(response):
    """
    步骤3: 解析汇率JSON数据
    """
    if response is None:
        return None
    
    # 答案: loads, text
    data = json.loads(response.text)
    
    print("\n解析后的汇率数据:")
    # 答案: base
    print(f"基础货币: {data['base']}")
    
    # 答案: date
    print(f"日期: {data['date']}")
    
    # 答案: rates, CNY
    cny_rate = data['rates']['CNY']
    print(f"1 JPY = {cny_rate} CNY")
    
    return data

def create_simple_chart():
    """
    步骤4: 创建简单的汇率图表
    使用模拟数据演示matplotlib基础用法
    """
    print("\n创建汇率图表...")
    
    # 模拟的日期和汇率数据
    dates = ['2025-06-20', '2025-06-21', '2025-06-22', '2025-06-23', '2025-06-24', '2025-06-25']
    rates = [0.045, 0.046, 0.044, 0.047, 0.045, 0.046]
    
    # 答案: plot
    plt.plot(dates, rates)
    
    # 答案: title
    plt.title("JPY to CNY Exchange Rate")
    
    # 答案: xlabel
    plt.xlabel("Date")
    
    # 答案: ylabel
    plt.ylabel("Exchange Rate")
    
    # 答案: grid
    plt.grid()
    
    # 旋转x轴标签以便更好显示
    plt.xticks(rotation=45)
    
    # 答案: show (保存图片而不是显示)
    plt.savefig('exchange_rate_chart.png')
    print("图表已保存为 exchange_rate_chart.png")

def main():
    """
    主程序：按顺序执行所有步骤
    """
    print("=" * 50)
    print("汇率API和图表练习 - 简单级别")
    print("=" * 50)
    
    # 执行所有步骤
    response = get_exchange_rate()
    show_raw_data(response)
    data = parse_exchange_data(response)
    create_simple_chart()
    
    print("\n练习完成！")

if __name__ == "__main__":
    main()
