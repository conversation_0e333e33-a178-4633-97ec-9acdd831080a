"""
练习模板 - 困难级别

项目要求：
创建一个完整的汇率追踪和可视化程序，实现以下功能：

核心功能：
1. 获取实时汇率数据
2. 获取历史汇率数据
3. 数据解析和处理
4. 创建汇率趋势图表
5. 汇率分析和统计

扩展功能 (挑战)：
6. 支持多种货币对比
7. 数据保存到文件
8. 汇率预警功能
9. 交互式用户界面

技术要求：
- 使用面向对象编程 (创建类)
- 实现完整的错误处理
- 代码结构清晰
- 用户界面友好

评分标准：
- 基础功能实现 (60分)
- 错误处理完善 (20分)  
- 代码结构清晰 (10分)
- 挑战功能实现 (10分)
"""

import requests
import json
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import csv

class CurrencyTracker:
    """
    汇率追踪器类
    
    你需要实现以下方法：
    - __init__: 初始化方法
    - get_current_rate: 获取当前汇率
    - get_historical_rates: 获取历史汇率
    - parse_data: 解析数据
    - create_chart: 创建图表
    - analyze_rates: 分析汇率
    - save_data: 保存数据 (挑战)
    - set_alert: 设置汇率预警 (挑战)
    - run: 主运行方法
    """
    
    def __init__(self, base_currency="JPY", target_currency="CNY"):
        """
        初始化汇率追踪器
        
        TODO: 设置必要的属性
        - 基础货币和目标货币
        - API基础URL
        - 数据存储
        - 其他需要的属性
        """
        pass
    
    def get_current_rate(self):
        """
        获取当前汇率
        
        TODO: 实现获取当前汇率的逻辑
        返回: 汇率值或None
        """
        pass
    
    def get_historical_rates(self, days=7):
        """
        获取历史汇率数据
        
        TODO: 实现获取历史数据的逻辑
        参数: days - 获取多少天的数据
        返回: (日期列表, 汇率列表) 或 (None, None)
        """
        pass
    
    def parse_data(self, response):
        """
        解析API响应数据
        
        TODO: 实现数据解析逻辑
        参数: response - API响应对象
        返回: 解析后的数据或None
        """
        pass
    
    def create_chart(self, dates, rates, title="Exchange Rate Trend"):
        """
        创建汇率图表
        
        TODO: 实现图表创建逻辑
        参数: 
        - dates: 日期列表
        - rates: 汇率列表
        - title: 图表标题
        """
        pass
    
    def analyze_rates(self, rates):
        """
        分析汇率数据
        
        TODO: 实现汇率分析逻辑
        - 计算平均值、最大值、最小值
        - 计算变化趋势
        - 显示统计信息
        参数: rates - 汇率列表
        """
        pass
    
    def save_data(self, dates, rates, filename="exchange_rates.csv"):
        """
        保存数据到文件 (挑战功能)
        
        TODO: 将汇率数据保存到CSV文件
        参数:
        - dates: 日期列表
        - rates: 汇率列表
        - filename: 文件名
        """
        pass
    
    def set_alert(self, target_rate, alert_type="above"):
        """
        设置汇率预警 (挑战功能)
        
        TODO: 实现汇率预警功能
        参数:
        - target_rate: 目标汇率
        - alert_type: 预警类型 ("above" 或 "below")
        """
        pass
    
    def display_menu(self):
        """
        显示用户菜单 (挑战功能)
        
        TODO: 实现交互式菜单
        """
        pass
    
    def run(self):
        """
        主运行方法
        
        TODO: 实现主程序逻辑
        - 显示欢迎信息
        - 提供功能选择
        - 调用相应的方法
        """
        pass

def main():
    """
    程序入口点
    """
    # TODO: 创建CurrencyTracker实例并运行
    pass

if __name__ == "__main__":
    main()
