"""
练习模板 - 简单级别

学习重点：
- requests.get()
- JSON数据解析
- plt.plot()
- plt.title(), plt.xlabel(), plt.ylabel()
- plt.grid(), plt.show()
"""

import requests
import json
import matplotlib.pyplot as plt

def get_exchange_rate():
    """
    步骤1: 获取汇率数据
    请根据提示填入正确的代码
    """
    print("开始获取汇率数据...")
    
    # 提示：汇率API的完整地址
    url = "https://api.frankfurter.app/latest?from=JPY&to=CNY"
    
    # TODO: 使用requests库发送GET请求
    # 提示：requests.get(参数)
    response = requests._____(url)
    
    # TODO: 获取并打印状态码
    # 提示：response有一个叫status_code的属性
    print(f"状态码: {response._______}")
    
    # TODO: 判断请求是否成功
    # 提示：状态码200表示成功
    if response._______ == ___:
        print("请求成功!")
        return response
    else:
        print("请求失败!")
        return None

def show_raw_data(response):
    """
    步骤2: 显示服务器返回的原始数据
    """
    if response is None:
        return
    
    print("\n服务器返回的原始数据:")
    # TODO: 打印response的text属性
    # 提示：response.text包含服务器返回的文本内容
    print(response._______)

def parse_exchange_data(response):
    """
    步骤3: 解析汇率JSON数据
    """
    if response is None:
        return None
    
    # TODO: 将JSON字符串转换为Python字典
    # 提示：使用json.loads()函数
    data = json._____(response._______)
    
    print("\n解析后的汇率数据:")
    # TODO: 获取基础货币字段
    # 提示：字典的键名是"base"
    print(f"基础货币: {data['_____']}")
    
    # TODO: 获取日期字段
    # 提示：字典的键名是"date"
    print(f"日期: {data['_____']}")
    
    # TODO: 获取汇率数据
    # 提示：先获取"rates"，再获取"CNY"
    cny_rate = data['_____']['_____']
    print(f"1 JPY = {cny_rate} CNY")
    
    return data

def create_simple_chart():
    """
    步骤4: 创建简单的汇率图表
    使用模拟数据演示matplotlib基础用法
    """
    print("\n创建汇率图表...")
    
    # 模拟的日期和汇率数据
    dates = ['2025-06-20', '2025-06-21', '2025-06-22', '2025-06-23', '2025-06-24', '2025-06-25']
    rates = [0.045, 0.046, 0.044, 0.047, 0.045, 0.046]
    
    # TODO: 创建线图
    # 提示：使用plt.plot(x轴数据, y轴数据)
    plt._____(dates, rates)
    
    # TODO: 设置图表标题
    # 提示：使用plt.title("标题文字")
    plt._____("JPY to CNY Exchange Rate")
    
    # TODO: 设置x轴标签
    # 提示：使用plt.xlabel("标签文字")
    plt._____("Date")
    
    # TODO: 设置y轴标签
    # 提示：使用plt.ylabel("标签文字")
    plt._____("Exchange Rate")
    
    # TODO: 显示网格
    # 提示：使用plt.grid()
    plt._____()
    
    # 旋转x轴标签以便更好显示
    plt.xticks(rotation=45)
    
    # TODO: 显示图表
    # 提示：使用plt.show()
    plt._____()

def main():
    """
    主程序：按顺序执行所有步骤
    """
    print("=" * 50)
    print("汇率API和图表练习 - 简单级别")
    print("=" * 50)
    
    # 执行所有步骤
    response = get_exchange_rate()
    show_raw_data(response)
    data = parse_exchange_data(response)
    create_simple_chart()
    
    print("\n练习完成！")

if __name__ == "__main__":
    main()

"""
答案提示：
1. get
2. status_code (出现3次)
3. 200
4. text (出现2次)
5. loads
6. base
7. date
8. rates (出现1次)
9. CNY
10. plot
11. title
12. xlabel
13. ylabel
14. grid
15. show
"""
