"""
练习模板 - 中等级别

学习重点：
- 独立编写分块下载代码
- 实现进度显示功能
- 完善异常处理机制
- 文件管理和验证
"""

import requests
import os
import time

def download_image_with_progress(url, filename):
    """
    任务1: 实现带进度显示的图片下载
    
    要求：
    1. 使用stream=True发送GET请求
    2. 检查响应状态码
    3. 获取文件总大小（content-length头）
    4. 分块下载并实时显示进度百分比
    5. 使用适当的异常处理
    6. 返回下载是否成功的布尔值
    
    参数：
    - url: 图片URL
    - filename: 保存的文件名
    
    返回：
    - bool: 下载成功返回True，失败返回False
    """
    print(f"开始下载: {filename}")
    
    # TODO: 在这里编写你的代码
    
    pass

def batch_download_images(image_configs):
    """
    任务2: 批量下载多张图片
    
    要求：
    1. 遍历图片配置列表
    2. 为每张图片调用download_image_with_progress函数
    3. 统计成功和失败的下载数量
    4. 显示总体下载报告
    5. 处理可能的异常情况
    
    参数：
    - image_configs: 图片配置列表，每个元素包含url和filename
    
    返回：
    - dict: 包含成功数量、失败数量和详细结果的字典
    """
    print("开始批量下载...")
    
    # TODO: 在这里编写你的代码
    
    pass

def validate_downloaded_files(filenames):
    """
    任务3: 验证下载的文件
    
    要求：
    1. 检查每个文件是否存在
    2. 获取并显示文件大小
    3. 验证文件大小是否合理（大于0字节）
    4. 计算所有文件的总大小
    5. 显示详细的验证报告
    
    参数：
    - filenames: 要验证的文件名列表
    
    返回：
    - dict: 包含验证结果的字典
    """
    print("\n验证下载的文件...")
    
    # TODO: 在这里编写你的代码
    
    pass

def cleanup_files(filenames):
    """
    任务4: 清理下载的文件（可选）
    
    要求：
    1. 询问用户是否要删除下载的文件
    2. 如果用户同意，删除所有指定的文件
    3. 处理删除过程中可能的异常
    4. 显示清理结果
    
    参数：
    - filenames: 要清理的文件名列表
    """
    print("\n文件清理选项...")
    
    # TODO: 在这里编写你的代码（可选任务）
    
    pass

def create_download_report(download_results, validation_results):
    """
    挑战任务: 生成下载报告（可选）
    
    要求：
    1. 创建一个详细的下载报告
    2. 包含下载统计、文件信息、时间戳等
    3. 将报告保存到文本文件
    4. 格式化输出，使报告易读
    
    参数：
    - download_results: 下载结果字典
    - validation_results: 验证结果字典
    """
    print("\n生成下载报告...")
    
    # TODO: 在这里编写你的代码（挑战任务）
    
    pass

def main():
    """
    主程序
    """
    print("=" * 50)
    print("图片下载练习 - 中等级别")
    print("=" * 50)
    
    # 定义要下载的图片配置
    image_configs = [
        {"url": "https://picsum.photos/800/600", "filename": "large_image.jpg"},
        {"url": "https://picsum.photos/400/300", "filename": "medium_image.jpg"},
        {"url": "https://picsum.photos/200/150", "filename": "small_image.jpg"},
    ]
    
    # 执行下载任务
    download_results = batch_download_images(image_configs)
    
    # 验证下载的文件
    filenames = [config["filename"] for config in image_configs]
    validation_results = validate_downloaded_files(filenames)
    
    # 生成报告（挑战任务）
    create_download_report(download_results, validation_results)
    
    # 清理文件（可选）
    cleanup_files(filenames)
    
    print("\n练习完成！")

if __name__ == "__main__":
    main()
