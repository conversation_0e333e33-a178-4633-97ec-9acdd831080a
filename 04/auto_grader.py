"""
自动测试评分系统
用于评估 practice_hard.py 的完成质量

评分标准：
- 基础下载功能 (30分)
- 进度显示和用户体验 (20分)
- 异常处理完善性 (20分)
- 代码结构和设计 (15分)
- 扩展功能实现 (15分)
"""

import importlib.util
import sys
import io
import contextlib
import traceback
import inspect
import requests
import os
import time
from datetime import datetime

class ImageDownloaderGrader:
    def __init__(self, student_file="practice_hard.py"):
        self.student_file = student_file
        self.total_score = 0
        self.max_score = 100
        self.test_results = []
        self.student_module = None
        self.test_files = []
        
    def load_student_code(self):
        """加载学生的代码文件"""
        try:
            spec = importlib.util.spec_from_file_location("student_code", self.student_file)
            self.student_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.student_module)
            return True
        except Exception as e:
            self.add_result("代码加载", 0, 5, f"无法加载代码文件: {e}")
            return False
    
    def add_result(self, test_name, score, max_score, comment=""):
        """添加测试结果"""
        self.test_results.append({
            'name': test_name,
            'score': score,
            'max_score': max_score,
            'comment': comment
        })
        self.total_score += score
    
    def test_class_structure(self):
        """测试类结构 (15分)"""
        print("测试类结构...")
        
        try:
            if hasattr(self.student_module, 'ImageDownloader'):
                downloader_class = self.student_module.ImageDownloader
                self.add_result("类定义", 3, 3, "ImageDownloader类定义正确")
                
                # 检查必要方法
                required_methods = [
                    '__init__', 'download_single_image', 'download_batch_images',
                    'show_progress', 'validate_file'
                ]
                
                method_score = 0
                for method in required_methods:
                    if hasattr(downloader_class, method):
                        method_score += 2
                
                self.add_result("必要方法", min(method_score, 10), 10, 
                              f"实现了 {method_score//2}/{len(required_methods)} 个必要方法")
                
                # 检查扩展方法
                optional_methods = ['calculate_speed', 'generate_report', 'manage_files', 'run_interactive']
                optional_score = 0
                for method in optional_methods:
                    if hasattr(downloader_class, method):
                        optional_score += 0.5
                
                self.add_result("扩展方法", min(optional_score, 2), 2, 
                              f"实现了扩展方法")
                
            else:
                self.add_result("类定义", 0, 15, "未找到ImageDownloader类")
                
        except Exception as e:
            self.add_result("类结构测试", 0, 15, f"测试异常: {e}")
    
    def test_basic_download(self):
        """测试基础下载功能 (30分)"""
        print("测试基础下载功能...")
        
        try:
            if hasattr(self.student_module, 'ImageDownloader'):
                downloader = self.student_module.ImageDownloader()
                
                # 测试单张图片下载
                test_url = "https://picsum.photos/100/100"
                test_filename = "test_download.jpg"
                self.test_files.append(test_filename)
                
                try:
                    result = downloader.download_single_image(test_url, test_filename, show_progress=False)
                    
                    if os.path.exists(test_filename):
                        file_size = os.path.getsize(test_filename)
                        if file_size > 0:
                            self.add_result("单张下载", 15, 15, f"成功下载文件，大小: {file_size} 字节")
                        else:
                            self.add_result("单张下载", 5, 15, "文件下载但大小为0")
                    else:
                        self.add_result("单张下载", 0, 15, "文件未成功下载")
                        
                except Exception as e:
                    self.add_result("单张下载", 0, 15, f"下载方法异常: {e}")
                
                # 测试批量下载
                try:
                    test_batch = [
                        {"url": "https://picsum.photos/50/50", "filename": "batch1.jpg"},
                        {"url": "https://picsum.photos/60/60", "filename": "batch2.jpg"}
                    ]
                    self.test_files.extend(["batch1.jpg", "batch2.jpg"])
                    
                    batch_result = downloader.download_batch_images(test_batch)
                    
                    success_count = 0
                    for item in test_batch:
                        if os.path.exists(item["filename"]):
                            success_count += 1
                    
                    if success_count == len(test_batch):
                        self.add_result("批量下载", 15, 15, f"成功下载 {success_count} 个文件")
                    elif success_count > 0:
                        self.add_result("批量下载", 10, 15, f"部分成功，下载了 {success_count} 个文件")
                    else:
                        self.add_result("批量下载", 0, 15, "批量下载失败")
                        
                except Exception as e:
                    self.add_result("批量下载", 0, 15, f"批量下载方法异常: {e}")
            else:
                self.add_result("基础下载功能", 0, 30, "未找到ImageDownloader类")
                
        except Exception as e:
            self.add_result("基础下载测试", 0, 30, f"测试异常: {e}")
    
    def test_progress_and_ux(self):
        """测试进度显示和用户体验 (20分)"""
        print("测试进度显示和用户体验...")
        
        try:
            if hasattr(self.student_module, 'ImageDownloader'):
                downloader = self.student_module.ImageDownloader()
                
                # 测试进度显示方法
                if hasattr(downloader, 'show_progress'):
                    try:
                        # 捕获输出测试进度显示
                        with io.StringIO() as buf, contextlib.redirect_stdout(buf):
                            downloader.show_progress(500, 1000, time.time())
                            output = buf.getvalue()
                        
                        if output.strip():
                            self.add_result("进度显示", 10, 10, "进度显示方法有输出")
                        else:
                            self.add_result("进度显示", 5, 10, "进度显示方法无输出")
                    except Exception as e:
                        self.add_result("进度显示", 0, 10, f"进度显示方法异常: {e}")
                else:
                    self.add_result("进度显示", 0, 10, "未实现show_progress方法")
                
                # 测试速度计算
                if hasattr(downloader, 'calculate_speed'):
                    try:
                        speed = downloader.calculate_speed(1024, 1.0)
                        if speed:
                            self.add_result("速度计算", 5, 5, "速度计算方法有返回值")
                        else:
                            self.add_result("速度计算", 2, 5, "速度计算方法返回空值")
                    except Exception as e:
                        self.add_result("速度计算", 0, 5, f"速度计算异常: {e}")
                else:
                    self.add_result("速度计算", 0, 5, "未实现calculate_speed方法")
                
                # 测试用户界面
                if hasattr(downloader, 'run_interactive'):
                    self.add_result("交互界面", 5, 5, "实现了交互界面方法")
                else:
                    self.add_result("交互界面", 0, 5, "未实现交互界面方法")
                    
        except Exception as e:
            self.add_result("进度和用户体验测试", 0, 20, f"测试异常: {e}")
    
    def test_error_handling(self):
        """测试异常处理 (20分)"""
        print("测试异常处理...")
        
        try:
            if hasattr(self.student_module, 'ImageDownloader'):
                downloader = self.student_module.ImageDownloader()
                
                # 测试无效URL处理
                try:
                    result = downloader.download_single_image("invalid_url", "test_invalid.jpg", show_progress=False)
                    self.add_result("无效URL处理", 10, 10, "处理了无效URL请求")
                except Exception as e:
                    self.add_result("无效URL处理", 5, 10, f"无效URL引发异常但被捕获: {type(e).__name__}")
                
                # 测试文件验证
                if hasattr(downloader, 'validate_file'):
                    try:
                        validation = downloader.validate_file("nonexistent_file.jpg")
                        self.add_result("文件验证", 10, 10, "实现了文件验证功能")
                    except Exception as e:
                        self.add_result("文件验证", 5, 10, f"文件验证方法异常: {e}")
                else:
                    self.add_result("文件验证", 0, 10, "未实现validate_file方法")
                    
        except Exception as e:
            self.add_result("异常处理测试", 0, 20, f"测试异常: {e}")
    
    def test_extended_features(self):
        """测试扩展功能 (15分)"""
        print("测试扩展功能...")
        
        try:
            if hasattr(self.student_module, 'ImageDownloader'):
                downloader = self.student_module.ImageDownloader()
                
                feature_score = 0
                
                # 测试报告生成
                if hasattr(downloader, 'generate_report'):
                    feature_score += 4
                
                # 测试文件管理
                if hasattr(downloader, 'manage_files'):
                    feature_score += 4
                
                # 测试自定义批量任务
                if hasattr(downloader, 'create_custom_batch'):
                    feature_score += 4
                
                # 检查代码复杂度和设计
                source = inspect.getsource(self.student_module.ImageDownloader)
                if len(source.split('\n')) > 100:
                    feature_score += 3
                
                self.add_result("扩展功能", feature_score, 15, f"实现了扩展功能")
                
        except Exception as e:
            self.add_result("扩展功能测试", 0, 15, f"测试异常: {e}")
    
    def cleanup_test_files(self):
        """清理测试文件"""
        for filename in self.test_files:
            try:
                if os.path.exists(filename):
                    os.remove(filename)
            except:
                pass
    
    def generate_report(self):
        """生成评分报告"""
        print("\n" + "="*60)
        print("图片下载器 - 自动评分报告")
        print("="*60)
        
        for result in self.test_results:
            status = "✓" if result['score'] == result['max_score'] else "✗" if result['score'] == 0 else "△"
            print(f"{status} {result['name']}: {result['score']}/{result['max_score']} 分")
            if result['comment']:
                print(f"   {result['comment']}")
        
        print("-"*60)
        print(f"总分: {self.total_score}/{self.max_score} 分")
        
        # 等级评定
        percentage = (self.total_score / self.max_score) * 100
        if percentage >= 90:
            grade = "优秀 (A)"
        elif percentage >= 80:
            grade = "良好 (B)"
        elif percentage >= 70:
            grade = "中等 (C)"
        elif percentage >= 60:
            grade = "及格 (D)"
        else:
            grade = "不及格 (F)"
        
        print(f"完成度: {percentage:.1f}%")
        print(f"等级: {grade}")
        
        # 改进建议
        print("\n改进建议:")
        if self.total_score < 30:
            print("- 重点完善基础下载功能")
        if self.total_score < 50:
            print("- 加强异常处理机制")
        if self.total_score < 70:
            print("- 改善用户体验和进度显示")
        if self.total_score < 85:
            print("- 实现更多扩展功能")
        
        print("="*60)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始自动评分...")
        
        if not self.load_student_code():
            return
        
        self.test_class_structure()
        self.test_basic_download()
        self.test_progress_and_ux()
        self.test_error_handling()
        self.test_extended_features()
        
        self.cleanup_test_files()
        self.generate_report()

def main():
    grader = ImageDownloaderGrader()
    grader.run_all_tests()

if __name__ == "__main__":
    main()
