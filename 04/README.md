# L4 - 图片API调用与分块下载

## 教学目标

本项目通过图片API学习Python网络编程进阶概念：

- **requests.get(stream=True)** - 流式下载基础
- **response.iter_content()** - 分块读取数据
- **异常处理 (try-except)** - 网络请求错误处理
- **文件操作 ("wb"模式)** - 二进制文件写入
- **进度显示** - 下载进度条实现
- **批量处理** - 多文件下载管理

## 核心概念讲解

### 为什么要分块下载？
1. **占内存少** - 不需要一次性加载整个文件到内存
2. **可以设置进度条** - 实时显示下载进度
3. **断点续传** - 支持下载中断后继续（概念讲解）

### 关键技术点
- **stream=True参数** - 保持连接打开，按需读取
- **iter_content(chunk_size)** - 迭代获取数据块
- **with语句** - 确保资源正确关闭
- **异常处理** - 处理网络中断、磁盘空间等问题
- **content-length头** - 获取文件总大小用于进度计算

## 文件说明

### 1. 分层次练习模板 (三个难度级别)

#### `practice_easy.py` - 简单级别
- **适合对象**: 初学者，第一次接触文件下载
- **练习方式**: 填空练习，重点概念有详细提示
- **学习重点**: stream参数、分块读取、文件写入、异常处理
- **完成时间**: 20-25分钟

#### `practice_medium.py` - 中等级别  
- **适合对象**: 有基础语法经验的学生
- **练习方式**: 半引导式，给出函数框架和要求
- **学习重点**: 独立实现下载逻辑、进度显示、错误处理
- **完成时间**: 30-40分钟

#### `practice_hard.py` - 困难级别
- **适合对象**: 有编程经验，想要挑战的学生
- **练习方式**: 只给出类框架，需要完全自主实现
- **学习重点**: 面向对象设计、批量下载、完整项目开发
- **完成时间**: 50-70分钟

### 2. `practice_answers.py` - 练习答案
- 完整的答案版本
- 供老师参考和学生对照

### 3. `auto_grader.py` - 自动评分
- **适用对象**: 困难级别练习 (`practice_hard.py`)
- **评分内容**: 8个维度，总分100分
- **功能特点**:
  - 自动测试下载功能
  - 检查异常处理完善性
  - 评估代码结构和设计
  - 测试扩展功能实现
- **使用方法**: `python auto_grader.py`

## API说明

使用的图片API: `https://picsum.photos/{宽度}/{高度}`

示例:
- `https://picsum.photos/800/600` - 下载800x600的随机图片
- `https://picsum.photos/400/300` - 下载400x300的随机图片

## 运行环境要求

- Python 3.6+
- requests库 (`pip install requests`)
- 网络连接
