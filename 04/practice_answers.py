"""
练习答案 - 所有级别参考答案

包含简单、中等、困难三个级别的完整实现
供教师参考和学生对照学习
"""

import requests
import os
import time
from datetime import datetime
import json

# ==================== 简单级别答案 ====================

def easy_download_image_basic():
    """简单级别 - 基础图片下载答案"""
    print("开始下载图片...")
    
    url = "https://picsum.photos/800/600"
    filename = "downloaded_image.jpg"
    
    response = requests.get(url, stream=True)
    
    if response.status_code == 200:
        print("请求成功，开始下载...")
        
        with open(filename, "wb") as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
        
        print(f"下载完成！文件保存为: {filename}")
    else:
        print(f"下载失败，状态码: {response.status_code}")

def easy_download_with_size_info():
    """简单级别 - 显示文件大小信息答案"""
    print("\n获取文件大小信息...")
    
    url = "https://picsum.photos/400/300"
    response = requests.get(url, stream=True)
    
    if response.status_code == 200:
        total_size = int(response.headers.get('content-length', 0))
        print(f"文件总大小: {total_size} 字节")
        
        filename = "image_with_size.jpg"
        downloaded_size = 0
        
        with open(filename, "wb") as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"下载进度: {progress:.1f}%", end="\r")
        
        print(f"\n下载完成！文件保存为: {filename}")

def easy_download_with_error_handling():
    """简单级别 - 异常处理答案"""
    print("\n带异常处理的下载...")
    
    url = "https://picsum.photos/600/400"
    filename = "safe_download.jpg"
    
    try:
        response = requests.get(url, stream=True)
        
        if response.status_code == 200:
            with open(filename, "wb") as f:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        f.write(chunk)
            print(f"下载成功: {filename}")
        else:
            print(f"HTTP错误: {response.status_code}")
            
    except requests.RequestException:
        print("网络请求失败")
    except IOError:
        print("文件操作失败")
    except Exception:
        print("发生未知错误")

# ==================== 中等级别答案 ====================

def medium_download_image_with_progress(url, filename):
    """中等级别 - 带进度显示的下载答案"""
    print(f"开始下载: {filename}")
    
    try:
        response = requests.get(url, stream=True)
        
        if response.status_code != 200:
            print(f"HTTP错误: {response.status_code}")
            return False
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0
        start_time = time.time()
        
        with open(filename, "wb") as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        elapsed_time = time.time() - start_time
                        if elapsed_time > 0:
                            speed = downloaded_size / elapsed_time / 1024  # KB/s
                            print(f"进度: {progress:.1f}% - 速度: {speed:.1f} KB/s", end="\r")
        
        print(f"\n下载完成: {filename}")
        return True
        
    except requests.RequestException as e:
        print(f"网络错误: {e}")
        return False
    except IOError as e:
        print(f"文件错误: {e}")
        return False
    except Exception as e:
        print(f"未知错误: {e}")
        return False

def medium_batch_download_images(image_configs):
    """中等级别 - 批量下载答案"""
    print("开始批量下载...")
    
    results = {
        'success_count': 0,
        'failed_count': 0,
        'details': []
    }
    
    for config in image_configs:
        url = config['url']
        filename = config['filename']
        
        success = medium_download_image_with_progress(url, filename)
        
        if success:
            results['success_count'] += 1
            results['details'].append({'filename': filename, 'status': 'success'})
        else:
            results['failed_count'] += 1
            results['details'].append({'filename': filename, 'status': 'failed'})
    
    print(f"\n批量下载完成: 成功 {results['success_count']} 个，失败 {results['failed_count']} 个")
    return results

# ==================== 困难级别答案 ====================

class ImageDownloader:
    """困难级别 - 完整的图片下载管理器答案"""
    
    def __init__(self):
        self.download_history = []
        self.default_chunk_size = 1024
        self.stats = {
            'total_downloads': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'total_bytes': 0
        }
    
    def download_single_image(self, url, filename, show_progress=True):
        """下载单张图片"""
        start_time = time.time()
        
        try:
            response = requests.get(url, stream=True)
            
            if response.status_code != 200:
                result = {
                    'success': False,
                    'filename': filename,
                    'error': f'HTTP {response.status_code}',
                    'size': 0,
                    'duration': time.time() - start_time
                }
                self.stats['failed_downloads'] += 1
                return result
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filename, "wb") as f:
                for chunk in response.iter_content(chunk_size=self.default_chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if show_progress and total_size > 0:
                            self.show_progress(downloaded_size, total_size, start_time)
            
            duration = time.time() - start_time
            result = {
                'success': True,
                'filename': filename,
                'size': downloaded_size,
                'duration': duration,
                'speed': self.calculate_speed(downloaded_size, duration)
            }
            
            self.stats['successful_downloads'] += 1
            self.stats['total_bytes'] += downloaded_size
            self.download_history.append(result)
            
            if show_progress:
                print(f"\n下载完成: {filename} ({downloaded_size} 字节)")
            
            return result
            
        except Exception as e:
            result = {
                'success': False,
                'filename': filename,
                'error': str(e),
                'size': 0,
                'duration': time.time() - start_time
            }
            self.stats['failed_downloads'] += 1
            return result
    
    def download_batch_images(self, image_list):
        """批量下载图片"""
        print(f"开始批量下载 {len(image_list)} 张图片...")
        
        results = []
        for i, image_info in enumerate(image_list, 1):
            print(f"\n[{i}/{len(image_list)}] ", end="")
            result = self.download_single_image(
                image_info['url'], 
                image_info['filename'],
                show_progress=True
            )
            results.append(result)
        
        success_count = sum(1 for r in results if r['success'])
        print(f"\n批量下载完成: {success_count}/{len(image_list)} 成功")
        
        return {
            'total': len(image_list),
            'success': success_count,
            'failed': len(image_list) - success_count,
            'details': results
        }
    
    def show_progress(self, downloaded, total, start_time):
        """显示下载进度"""
        if total <= 0:
            return
        
        progress = (downloaded / total) * 100
        elapsed_time = time.time() - start_time
        
        if elapsed_time > 0:
            speed = downloaded / elapsed_time
            speed_str = self.calculate_speed(downloaded, elapsed_time)
            
            eta = (total - downloaded) / speed if speed > 0 else 0
            eta_str = f"{eta:.0f}s" if eta < 60 else f"{eta/60:.1f}m"
            
            print(f"进度: {progress:.1f}% | 速度: {speed_str} | 剩余: {eta_str}", end="\r")
    
    def calculate_speed(self, bytes_downloaded, time_elapsed):
        """计算下载速度"""
        if time_elapsed <= 0:
            return "0 B/s"
        
        speed = bytes_downloaded / time_elapsed
        
        if speed < 1024:
            return f"{speed:.1f} B/s"
        elif speed < 1024 * 1024:
            return f"{speed/1024:.1f} KB/s"
        else:
            return f"{speed/(1024*1024):.1f} MB/s"
    
    def validate_file(self, filename, expected_size=None):
        """验证下载的文件"""
        result = {
            'filename': filename,
            'exists': False,
            'size': 0,
            'valid': False
        }
        
        if os.path.exists(filename):
            result['exists'] = True
            result['size'] = os.path.getsize(filename)
            
            if result['size'] > 0:
                result['valid'] = True
                if expected_size and abs(result['size'] - expected_size) > expected_size * 0.1:
                    result['valid'] = False
                    result['error'] = "文件大小异常"
        
        return result
    
    def generate_report(self, download_results):
        """生成下载报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': self.stats.copy(),
            'details': download_results
        }
        
        report_filename = f"download_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"报告已保存: {report_filename}")
        except Exception as e:
            print(f"保存报告失败: {e}")
    
    def run_interactive(self):
        """运行交互式界面"""
        while True:
            print("\n" + "="*50)
            print("图片下载管理器")
            print("="*50)
            print("1. 下载单张图片")
            print("2. 批量下载图片")
            print("3. 查看下载统计")
            print("4. 生成下载报告")
            print("5. 退出")
            
            choice = input("\n请选择功能 (1-5): ").strip()
            
            if choice == '1':
                url = input("请输入图片URL: ").strip()
                filename = input("请输入保存文件名: ").strip()
                if url and filename:
                    self.download_single_image(url, filename)
            
            elif choice == '2':
                print("批量下载示例...")
                images = [
                    {"url": "https://picsum.photos/400/300", "filename": "batch1.jpg"},
                    {"url": "https://picsum.photos/500/400", "filename": "batch2.jpg"},
                    {"url": "https://picsum.photos/600/500", "filename": "batch3.jpg"}
                ]
                self.download_batch_images(images)
            
            elif choice == '3':
                print(f"\n下载统计:")
                print(f"总下载次数: {self.stats['total_downloads']}")
                print(f"成功: {self.stats['successful_downloads']}")
                print(f"失败: {self.stats['failed_downloads']}")
                print(f"总下载量: {self.stats['total_bytes']} 字节")
            
            elif choice == '4':
                if self.download_history:
                    self.generate_report(self.download_history)
                else:
                    print("暂无下载记录")
            
            elif choice == '5':
                print("感谢使用！")
                break
            
            else:
                print("无效选择，请重试")

def main():
    """主程序入口"""
    downloader = ImageDownloader()
    downloader.run_interactive()

if __name__ == "__main__":
    main()
