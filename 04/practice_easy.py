"""
练习模板 - 简单级别

学习重点：
- requests.get(stream=True)
- response.iter_content()
- 异常处理 (try-except)
- 文件操作 ("wb"模式)
"""

import requests
import os

def download_image_basic():
    """
    步骤1: 基础图片下载
    请根据提示填入正确的代码
    """
    print("开始下载图片...")
    
    # 图片API地址
    url = "https://picsum.photos/800/600"
    filename = "downloaded_image.jpg"
    
    # TODO: 发送GET请求，注意设置stream参数
    # 提示：requests.get(url, stream=True)
    response = requests._____(url, stream=_____)
    
    # TODO: 检查请求状态码
    # 提示：状态码200表示成功
    if response._______ == ___:
        print("请求成功，开始下载...")
        
        # TODO: 打开文件进行写入，注意使用"wb"模式
        # 提示：open(filename, "wb")
        with open(filename, "_____") as f:
            # TODO: 使用iter_content进行分块下载
            # 提示：response.iter_content(chunk_size=1024)
            for chunk in response._______(chunk_size=_____):
                if chunk:
                    # TODO: 将数据块写入文件
                    # 提示：f.write(chunk)
                    f._____(chunk)
        
        print(f"下载完成！文件保存为: {filename}")
    else:
        print(f"下载失败，状态码: {response._______}")

def download_with_size_info():
    """
    步骤2: 显示文件大小信息
    """
    print("\n获取文件大小信息...")
    
    url = "https://picsum.photos/1920/1080"
    
    # TODO: 发送请求获取响应
    response = requests._____(url, stream=_____)
    
    if response._______ == 200:
        # TODO: 获取文件总大小
        # 提示：response.headers.get('content-length', 0)
        total_size = int(response.headers.get('_______', 0))
        print(f"文件总大小: {total_size} 字节")
        
        filename = "image_with_size.jpg"
        downloaded_size = 0
        
        with open(filename, "wb") as f:
            # TODO: 分块下载并统计已下载大小
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
                    # TODO: 累加已下载大小
                    # 提示：len(chunk)可以获取数据块大小
                    downloaded_size += _____(chunk)
                    
                    # 显示下载进度
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"下载进度: {progress:.1f}%", end="\r")
        
        print(f"\n下载完成！文件保存为: {filename}")

def download_with_error_handling():
    """
    步骤3: 添加异常处理
    """
    print("\n带异常处理的下载...")
    
    url = "https://picsum.photos/600/400"
    filename = "safe_download.jpg"
    
    # TODO: 使用try-except处理可能的异常
    try:
        response = requests.get(url, stream=True)
        
        if response.status_code == 200:
            with open(filename, "wb") as f:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        f.write(chunk)
            print(f"下载成功: {filename}")
        else:
            print(f"HTTP错误: {response.status_code}")
            
    # TODO: 捕获网络请求异常
    # 提示：requests.RequestException
    except requests._______:
        print("网络请求失败")
    # TODO: 捕获文件操作异常  
    # 提示：IOError
    except _______:
        print("文件操作失败")
    # TODO: 捕获其他异常
    # 提示：Exception
    except _______:
        print("发生未知错误")

def check_file_info():
    """
    步骤4: 检查下载的文件信息
    """
    print("\n检查下载的文件...")
    
    files = ["downloaded_image.jpg", "image_with_size.jpg", "safe_download.jpg"]
    
    for filename in files:
        # TODO: 检查文件是否存在
        # 提示：os.path.exists(filename)
        if os.path._____(_____):
            # TODO: 获取文件大小
            # 提示：os.path.getsize(filename)
            size = os.path._____(filename)
            print(f"{filename}: {size} 字节")
        else:
            print(f"{filename}: 文件不存在")

def main():
    """
    主程序：按顺序执行所有步骤
    """
    print("=" * 50)
    print("图片下载练习 - 简单级别")
    print("=" * 50)
    
    download_image_basic()
    download_with_size_info()
    download_with_error_handling()
    check_file_info()
    
    print("\n练习完成！")

if __name__ == "__main__":
    main()

"""
答案提示：
1. get, True (第一个requests.get)
2. status_code (出现多次)
3. 200 (出现多次)
4. wb
5. iter_content
6. 1024
7. write
8. get, True (第二个requests.get)
9. content-length
10. len
11. RequestException
12. IOError
13. Exception
14. exists, filename
15. getsize
"""
