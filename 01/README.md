# L1

## 📚 教学目标

本项目通过国际空间站位置API学习Python网络编程基础概念：

- **requests.get()** - 发送HTTP请求
- **response.status_code** - 检查请求状态
- **response.text** - 获取响应内容
- **时间戳概念** - 理解时间的数字表示

## 🗂️ 文件说明

### 1. 分层次练习模板 (三个难度级别)

#### `practice_easy.py` - 简单级别 ⭐
- **适合对象**: 完全初学者，第一次接触网络编程
- **练习方式**: 填空练习，有详细提示
- **学习重点**: 基础语法和概念理解
- **完成时间**: 15-20分钟

#### `practice_medium.py` - 中等级别 ⭐⭐
- **适合对象**: 有Python基础，了解基本语法
- **练习方式**: 半引导式，给出函数框架和要求
- **学习重点**: 独立编写逻辑，理解错误处理
- **完成时间**: 25-35分钟

#### `practice_hard.py` - 困难级别 ⭐⭐⭐
- **适合对象**: 有编程经验，想要挑战自己
- **练习方式**: 只给出类框架，需要完全自主实现
- **学习重点**: 面向对象编程，完整项目开发
- **完成时间**: 45-60分钟

### 2. `practice_answers.py` - 练习答案
- 完整的答案版本
- 供老师参考和学生对照

### 3. `auto_grader.py` - 自动评分 🤖
- **适用对象**: 困难级别练习 (`practice_hard.py`)
- **评分内容**: 7个维度，总分100分
- **功能特点**:
  - 自动测试代码功能
  - 详细的评分报告
  - 等级评定和改进建议
- **使用方法**: `python auto_grader.py`
