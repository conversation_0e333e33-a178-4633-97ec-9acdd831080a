"""
示例学生代码 - 用于测试自动评分系统
这是一个部分完成的practice_hard.py实现
"""

import requests
import json
from datetime import datetime
import time

class ISSTracker:
    """
    国际空间站追踪器 - 示例实现
    """
    
    def __init__(self):
        """初始化"""
        self.url = "http://api.open-notify.org/iss-now.json"
        self.data_history = []
    
    def get_iss_position(self):
        """获取ISS位置"""
        print("🚀 正在获取国际空间站位置...")
        
        try:
            response = requests.get(self.url)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 获取成功!")
                return response
            else:
                print("❌ 获取失败!")
                return None
                
        except Exception as e:
            print(f"❌ 网络错误: {e}")
            return None
    
    def parse_data(self, response):
        """解析数据"""
        if response is None:
            return None
        
        try:
            data = json.loads(response.text)
            return data
        except json.JSONDecodeError:
            print("❌ 数据解析失败!")
            return None
    
    def display_info(self, data):
        """显示信息"""
        if data is None:
            print("❌ 没有数据可显示")
            return
        
        print(f"\n🌍 国际空间站位置:")
        print(f"纬度: {data['iss_position']['latitude']}°")
        print(f"经度: {data['iss_position']['longitude']}°")
        
        # 转换时间戳
        timestamp = data['timestamp']
        readable_time = datetime.fromtimestamp(timestamp)
        print(f"时间: {readable_time}")
    
    def save_data(self, data):
        """保存数据 - 挑战功能"""
        if data is None:
            return
        
        self.data_history.append(data)
        print(f"📝 数据已保存，历史记录: {len(self.data_history)} 条")
    
    def run(self):
        """主运行方法"""
        print("🛰️ 国际空间站追踪器")
        print("1. 获取当前位置")
        print("2. 退出")
        
        choice = input("请选择 (1/2): ")
        
        if choice == '1':
            response = self.get_iss_position()
            data = self.parse_data(response)
            self.display_info(data)
            self.save_data(data)
        else:
            print("👋 再见!")

def main():
    """程序入口"""
    tracker = ISSTracker()
    tracker.run()

if __name__ == "__main__":
    main()
