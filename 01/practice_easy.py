"""
练习模板 - 简单级别

学习重点：
- requests.get()
- response.status_code  
- response.text
- 时间戳概念
"""

# 导入需要的库
import requests
import json
from datetime import datetime

def get_iss_data():
    """
    步骤1: 获取国际空间站数据
    请根据提示填入正确的代码
    """
    print("🚀 开始获取国际空间站位置...")
    
    # 提示：国际空间站API的完整地址
    url = "http://api.open-notify.org/iss-now.json"
    
    # TODO: 使用requests库发送GET请求
    # 提示：requests.get(参数)
    response = requests._____(url)
    
    # TODO: 获取并打印状态码
    # 提示：response有一个叫status_code的属性
    print(f"状态码: {response._______}")
    
    # TODO: 判断请求是否成功
    # 提示：状态码200表示成功
    if response._______ == ___:
        print("✅ 请求成功!")
        return response
    else:
        print("❌ 请求失败!")
        return None

def show_raw_data(response):
    """
    步骤2: 显示服务器返回的原始数据
    """
    if response is None:
        return
    
    print("\n📄 服务器返回的原始数据:")
    # TODO: 打印response的text属性
    # 提示：response.text包含服务器返回的文本内容
    print(response._______)

def parse_data(response):
    """
    步骤3: 解析JSON数据
    """
    if response is None:
        return None
    
    # TODO: 将JSON字符串转换为Python字典
    # 提示：使用json.loads()函数
    data = json._____(response._______)
    
    print("\n🌍 解析后的数据:")
    # TODO: 获取消息字段
    # 提示：字典的键名是"message"
    print(f"消息: {data['_______']}")
    
    # TODO: 获取时间戳字段
    # 提示：字典的键名是"timestamp"
    print(f"时间戳: {data['_______']}")
    
    # TODO: 获取纬度信息
    # 提示：先获取"iss_position"，再获取"latitude"
    print(f"纬度: {data['_______']['_______']}")
    
    # TODO: 获取经度信息
    # 提示：先获取"iss_position"，再获取"longitude"
    print(f"经度: {data['_______']['_______']}")
    
    return data

def convert_timestamp(data):
    """
    步骤4: 转换时间戳
    """
    if data is None:
        return
    
    # TODO: 从数据中获取时间戳
    # 提示：时间戳的键名是"timestamp"
    timestamp = data['_______']
    
    print(f"\n⏰ 时间戳转换:")
    print(f"时间戳数字: {timestamp}")
    
    # TODO: 将时间戳转换为可读时间
    # 提示：使用datetime.fromtimestamp()函数
    readable_time = datetime.fromtimestamp(_______)
    print(f"转换为时间: {readable_time}")

def main():
    """
    主程序：按顺序执行所有步骤
    """
    print("=" * 50)
    print("🛰️  国际空间站位置练习 - 简单级别")
    print("=" * 50)
    
    # 执行所有步骤
    response = get_iss_data()
    show_raw_data(response)
    data = parse_data(response)
    convert_timestamp(data)
    
    print("\n🎉 练习完成！")

if __name__ == "__main__":
    main()

"""
💡 答案提示：
1. get
2. status_code (出现3次)
3. 200
4. text (出现2次)
5. loads
6. message
7. timestamp (出现2次)
8. iss_position (出现2次)
9. latitude
10. longitude
11. timestamp (变量名)
"""
